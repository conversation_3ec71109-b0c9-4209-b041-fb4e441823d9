#!/usr/bin/env python3
"""
Phase 1: Data Collection and Inventory
BCMA CAR-T Therapy Resistance Analysis Pipeline

This module handles the acquisition and organization of publicly available 
longitudinal scRNA-seq datasets focusing on BCMA CAR-T therapy.

Priority datasets:
- GSE210079: BCMA CAR-T therapy in multiple myeloma
- GSE164551: Single-cell analysis of CAR-T therapy
- GSE246342: Longitudinal CAR-T therapy response
- GSE274185: BCMA CAR-T resistance mechanisms
- GSE143317: Multiple myeloma CAR-T treatment

Author: BCMA CAR-T Analysis Pipeline
Date: 2025-06-29
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import subprocess
import requests
import gzip
import tarfile
from typing import Dict, List, Tuple, Optional
import json
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_collection.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DataCollector:
    """
    Main class for collecting and organizing scRNA-seq datasets
    """
    
    def __init__(self, base_dir: str = "data"):
        """
        Initialize the DataCollector
        
        Args:
            base_dir: Base directory for storing downloaded data
        """
        self.base_dir = Path(base_dir)
        self.raw_data_dir = self.base_dir / "raw"
        self.processed_data_dir = self.base_dir / "processed"
        self.metadata_dir = self.base_dir / "metadata"
        
        # Create directory structure
        self._create_directories()
        
        # Dataset information
        self.datasets = {
            "GSE210079": {
                "title": "BCMA CAR-T therapy in multiple myeloma",
                "description": "Longitudinal scRNA-seq analysis of BCMA CAR-T therapy",
                "timepoints": ["pre-treatment", "post-treatment", "relapse"],
                "url": "https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE210079"
            },
            "GSE164551": {
                "title": "Single-cell analysis of CAR-T therapy",
                "description": "CAR-T cell dynamics and resistance mechanisms",
                "timepoints": ["baseline", "response", "progression"],
                "url": "https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE164551"
            },
            "GSE246342": {
                "title": "Longitudinal CAR-T therapy response",
                "description": "Temporal analysis of CAR-T therapy outcomes",
                "timepoints": ["pre-treatment", "early-response", "late-response"],
                "url": "https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE246342"
            },
            "GSE274185": {
                "title": "BCMA CAR-T resistance mechanisms",
                "description": "Mechanisms of resistance to BCMA CAR-T therapy",
                "timepoints": ["baseline", "resistance"],
                "url": "https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE274185"
            },
            "GSE143317": {
                "title": "Multiple myeloma CAR-T treatment",
                "description": "CAR-T therapy in multiple myeloma patients",
                "timepoints": ["pre-treatment", "post-treatment"],
                "url": "https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE143317"
            }
        }
        
        logger.info(f"DataCollector initialized with base directory: {self.base_dir}")
    
    def _create_directories(self):
        """Create necessary directory structure"""
        directories = [
            self.base_dir,
            self.raw_data_dir,
            self.processed_data_dir,
            self.metadata_dir,
            self.base_dir / "logs",
            self.base_dir / "scripts",
            self.base_dir / "results"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")
    
    def check_dependencies(self) -> bool:
        """
        Check if required tools are installed
        
        Returns:
            bool: True if all dependencies are available
        """
        dependencies = {
            "wget": "wget --version",
            "curl": "curl --version",
            "python": "python --version"
        }
        
        missing_deps = []
        
        for tool, command in dependencies.items():
            try:
                result = subprocess.run(
                    command.split(), 
                    capture_output=True, 
                    text=True, 
                    check=True
                )
                logger.info(f"✓ {tool} is available")
            except (subprocess.CalledProcessError, FileNotFoundError):
                missing_deps.append(tool)
                logger.error(f"✗ {tool} is not available")
        
        if missing_deps:
            logger.error(f"Missing dependencies: {missing_deps}")
            return False
        
        return True
    
    def download_geo_metadata(self, geo_id: str) -> Dict:
        """
        Download metadata for a GEO dataset
        
        Args:
            geo_id: GEO accession ID (e.g., GSE210079)
            
        Returns:
            Dict: Metadata information
        """
        logger.info(f"Downloading metadata for {geo_id}")
        
        # GEO metadata URL
        metadata_url = f"https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc={geo_id}&targ=self&form=text&view=brief"
        
        try:
            response = requests.get(metadata_url, timeout=30)
            response.raise_for_status()
            
            # Save metadata
            metadata_file = self.metadata_dir / f"{geo_id}_metadata.txt"
            with open(metadata_file, 'w') as f:
                f.write(response.text)
            
            logger.info(f"Metadata saved to {metadata_file}")
            
            # Parse basic information
            metadata = {
                "geo_id": geo_id,
                "download_date": datetime.now().isoformat(),
                "metadata_file": str(metadata_file),
                "status": "metadata_downloaded"
            }
            
            return metadata
            
        except Exception as e:
            logger.error(f"Failed to download metadata for {geo_id}: {e}")
            return {"geo_id": geo_id, "status": "metadata_failed", "error": str(e)}
    
    def download_dataset(self, geo_id: str) -> bool:
        """
        Download a complete GEO dataset
        
        Args:
            geo_id: GEO accession ID
            
        Returns:
            bool: True if download successful
        """
        logger.info(f"Starting download for dataset {geo_id}")
        
        # Create dataset-specific directory
        dataset_dir = self.raw_data_dir / geo_id
        dataset_dir.mkdir(exist_ok=True)
        
        # Download using wget (more reliable for large files)
        geo_url = f"https://www.ncbi.nlm.nih.gov/geo/download/?acc={geo_id}&format=file"
        
        try:
            # Download the tar file
            tar_file = dataset_dir / f"{geo_id}_RAW.tar"
            
            download_cmd = [
                "wget", 
                "-O", str(tar_file),
                "-c",  # Continue partial downloads
                "--timeout=300",
                "--tries=3",
                geo_url
            ]
            
            logger.info(f"Executing: {' '.join(download_cmd)}")
            result = subprocess.run(download_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Successfully downloaded {geo_id}")
                
                # Extract if it's a tar file
                if tar_file.exists():
                    self._extract_dataset(tar_file, dataset_dir)
                
                return True
            else:
                logger.error(f"Download failed for {geo_id}: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Exception during download of {geo_id}: {e}")
            return False
    
    def _extract_dataset(self, tar_file: Path, extract_dir: Path):
        """
        Extract downloaded tar file
        
        Args:
            tar_file: Path to tar file
            extract_dir: Directory to extract to
        """
        try:
            logger.info(f"Extracting {tar_file}")
            
            with tarfile.open(tar_file, 'r') as tar:
                tar.extractall(path=extract_dir)
            
            logger.info(f"Extraction completed to {extract_dir}")
            
            # Remove tar file to save space
            tar_file.unlink()
            logger.info(f"Removed tar file {tar_file}")
            
        except Exception as e:
            logger.error(f"Failed to extract {tar_file}: {e}")
    
    def create_dataset_inventory(self) -> pd.DataFrame:
        """
        Create an inventory of all available datasets
        
        Returns:
            pd.DataFrame: Dataset inventory
        """
        logger.info("Creating dataset inventory")
        
        inventory_data = []
        
        for geo_id, info in self.datasets.items():
            # Check if dataset exists locally
            dataset_dir = self.raw_data_dir / geo_id
            local_status = "available" if dataset_dir.exists() else "not_downloaded"
            
            # Count files if available
            file_count = 0
            if dataset_dir.exists():
                file_count = len(list(dataset_dir.glob("*")))
            
            inventory_data.append({
                "geo_id": geo_id,
                "title": info["title"],
                "description": info["description"],
                "timepoints": ", ".join(info["timepoints"]),
                "local_status": local_status,
                "file_count": file_count,
                "local_path": str(dataset_dir),
                "url": info["url"]
            })
        
        inventory_df = pd.DataFrame(inventory_data)
        
        # Save inventory
        inventory_file = self.metadata_dir / "dataset_inventory.csv"
        inventory_df.to_csv(inventory_file, index=False)
        
        logger.info(f"Dataset inventory saved to {inventory_file}")
        return inventory_df
    
    def download_all_datasets(self) -> Dict[str, bool]:
        """
        Download all priority datasets
        
        Returns:
            Dict: Download status for each dataset
        """
        logger.info("Starting download of all priority datasets")
        
        if not self.check_dependencies():
            logger.error("Missing required dependencies. Please install them first.")
            return {}
        
        download_status = {}
        
        for geo_id in self.datasets.keys():
            logger.info(f"Processing dataset {geo_id}")
            
            # Download metadata first
            metadata = self.download_geo_metadata(geo_id)
            
            # Download dataset
            success = self.download_dataset(geo_id)
            download_status[geo_id] = success
            
            if success:
                logger.info(f"✓ Successfully processed {geo_id}")
            else:
                logger.error(f"✗ Failed to process {geo_id}")
        
        # Create final inventory
        self.create_dataset_inventory()
        
        # Save download status
        status_file = self.metadata_dir / "download_status.json"
        with open(status_file, 'w') as f:
            json.dump(download_status, f, indent=2)
        
        logger.info(f"Download status saved to {status_file}")
        return download_status

def main():
    """Main execution function"""
    logger.info("Starting Phase 1: Data Collection and Inventory")
    
    # Initialize data collector
    collector = DataCollector()
    
    # Download all datasets
    status = collector.download_all_datasets()
    
    # Print summary
    successful = sum(status.values())
    total = len(status)
    
    logger.info(f"Data collection completed: {successful}/{total} datasets downloaded successfully")
    
    if successful == total:
        logger.info("✓ All datasets downloaded successfully!")
    else:
        failed_datasets = [geo_id for geo_id, success in status.items() if not success]
        logger.warning(f"Failed datasets: {failed_datasets}")

if __name__ == "__main__":
    main()
