#!/usr/bin/env python3
"""
BCMA CAR-T Therapy Resistance Analysis Pipeline
Main Pipeline Runner

This is the main execution script for the comprehensive single-cell RNA sequencing 
analysis pipeline for studying BCMA CAR-T therapy resistance mechanisms in 
multiple myeloma patients.

Pipeline Phases:
1. Data Collection and Inventory
2. Single-Cell Quality Control  
3. Batch Integration
4. Cell Type Annotation
5. Resistance Mechanism Analysis

Author: BCMA CAR-T Analysis Pipeline
Date: 2025-06-29
"""

import os
import sys
import logging
import argparse
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

# Import pipeline phases
from phase1_data_collection import DataCollector
from phase2_quality_control import QualityController
from phase3_batch_integration import BatchIntegrator
from phase4_cell_annotation import CellAnnotator
from phase5_resistance_analysis import ResistanceAnalyzer

# Configure logging
def setup_logging(log_level: str = "INFO", log_file: str = "pipeline.log"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )

logger = logging.getLogger(__name__)

class BCMAPipeline:
    """
    Main pipeline class for BCMA CAR-T resistance analysis
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize the pipeline
        
        Args:
            config_file: Path to configuration file
        """
        self.config = self.load_config(config_file)
        self.start_time = datetime.now()
        
        # Create main results directory
        self.results_dir = Path(self.config.get('results_dir', 'results'))
        self.results_dir.mkdir(exist_ok=True)
        
        logger.info("BCMA CAR-T Resistance Analysis Pipeline initialized")
        logger.info(f"Results directory: {self.results_dir}")
    
    def load_config(self, config_file: Optional[str] = None) -> Dict:
        """
        Load pipeline configuration
        
        Args:
            config_file: Path to configuration file
            
        Returns:
            Configuration dictionary
        """
        default_config = {
            'data_dir': 'data',
            'results_dir': 'results',
            'phases_to_run': [1, 2, 3, 4, 5],
            'datasets': ['GSE210079', 'GSE164551', 'GSE246342', 'GSE274185', 'GSE143317'],
            'integration_methods': ['harmony', 'scvi'],
            'qc_params': {
                'min_genes_per_cell': 200,
                'max_genes_per_cell': 5000,
                'max_mito_percent': 20,
                'doublet_threshold': 0.25
            },
            'annotation_confidence_threshold': 0.8,
            'n_cores': 4,
            'memory_limit': '32GB'
        }
        
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
                logger.info(f"Loaded configuration from {config_file}")
            except Exception as e:
                logger.warning(f"Failed to load config file {config_file}: {e}")
                logger.info("Using default configuration")
        else:
            logger.info("Using default configuration")
        
        return default_config
    
    def run_phase1(self) -> bool:
        """
        Run Phase 1: Data Collection and Inventory
        
        Returns:
            Success status
        """
        logger.info("=" * 60)
        logger.info("PHASE 1: DATA COLLECTION AND INVENTORY")
        logger.info("=" * 60)
        
        try:
            collector = DataCollector(base_dir=self.config['data_dir'])
            
            # Check dependencies
            if not collector.check_dependencies():
                logger.error("Missing required dependencies for data collection")
                return False
            
            # Download datasets
            download_status = collector.download_all_datasets()
            
            # Check if at least one dataset was downloaded successfully
            successful_downloads = sum(download_status.values())
            if successful_downloads == 0:
                logger.error("No datasets were downloaded successfully")
                return False
            
            logger.info(f"Phase 1 completed: {successful_downloads}/{len(download_status)} datasets downloaded")
            return True
            
        except Exception as e:
            logger.error(f"Phase 1 failed: {e}")
            return False
    
    def run_phase2(self) -> bool:
        """
        Run Phase 2: Single-Cell Quality Control
        
        Returns:
            Success status
        """
        logger.info("=" * 60)
        logger.info("PHASE 2: SINGLE-CELL QUALITY CONTROL")
        logger.info("=" * 60)
        
        try:
            qc = QualityController(
                data_dir=f"{self.config['data_dir']}/raw",
                results_dir=f"{self.config['results_dir']}/qc"
            )
            
            # Update QC parameters from config
            qc.qc_params.update(self.config.get('qc_params', {}))
            
            # Find and process datasets
            data_dir = Path(f"{self.config['data_dir']}/raw")
            datasets = list(data_dir.glob("GSE*"))
            
            if not datasets:
                logger.error("No datasets found for quality control")
                return False
            
            successful_qc = 0
            for dataset_dir in datasets:
                geo_id = dataset_dir.name
                logger.info(f"Processing {geo_id} for quality control")
                
                # Look for data files
                data_files = (
                    list(dataset_dir.glob("*.h5ad")) +
                    list(dataset_dir.glob("*.h5")) +
                    list(dataset_dir.glob("matrix.mtx*")) +
                    list(dataset_dir.glob("*.csv*")) +
                    list(dataset_dir.glob("*.tsv*"))
                )
                
                if data_files:
                    try:
                        adata, stats = qc.process_dataset(data_files[0], geo_id)
                        successful_qc += 1
                        logger.info(f"✓ Successfully processed {geo_id}")
                    except Exception as e:
                        logger.error(f"✗ Failed to process {geo_id}: {e}")
                else:
                    logger.warning(f"No suitable data files found in {dataset_dir}")
            
            if successful_qc == 0:
                logger.error("No datasets passed quality control")
                return False
            
            logger.info(f"Phase 2 completed: {successful_qc}/{len(datasets)} datasets passed QC")
            return True
            
        except Exception as e:
            logger.error(f"Phase 2 failed: {e}")
            return False
    
    def run_phase3(self) -> bool:
        """
        Run Phase 3: Batch Integration
        
        Returns:
            Success status
        """
        logger.info("=" * 60)
        logger.info("PHASE 3: BATCH INTEGRATION")
        logger.info("=" * 60)
        
        try:
            integrator = BatchIntegrator(
                data_dir=f"{self.config['results_dir']}/qc",
                results_dir=f"{self.config['results_dir']}/integration"
            )
            
            # Run integration with specified methods
            methods = self.config.get('integration_methods', ['harmony'])
            adata_integrated, metrics = integrator.integrate_datasets(methods)
            
            logger.info(f"Phase 3 completed: Integration successful with methods {methods}")
            return True
            
        except Exception as e:
            logger.error(f"Phase 3 failed: {e}")
            return False
    
    def run_phase4(self) -> bool:
        """
        Run Phase 4: Cell Type Annotation
        
        Returns:
            Success status
        """
        logger.info("=" * 60)
        logger.info("PHASE 4: CELL TYPE ANNOTATION")
        logger.info("=" * 60)
        
        try:
            annotator = CellAnnotator(
                data_dir=f"{self.config['results_dir']}/integration",
                results_dir=f"{self.config['results_dir']}/annotation"
            )
            
            # Load integrated data
            adata = annotator.load_integrated_data()
            
            # Calculate marker scores
            adata = annotator.calculate_marker_scores(adata)
            
            # Perform automated annotation
            adata = annotator.celltypist_annotation(adata)
            
            # Perform TCR analysis
            adata = annotator.tcr_analysis(adata)
            
            # Perform manual annotation
            adata = annotator.manual_annotation(adata)
            
            # Create consensus annotation
            adata = annotator.consensus_annotation(adata)
            
            # Generate plots
            annotator.generate_annotation_plots(adata)
            
            # Save annotated data
            output_path = annotator.results_dir / "annotated_data.h5ad"
            adata.write(output_path)
            
            logger.info("Phase 4 completed: Cell type annotation successful")
            return True
            
        except Exception as e:
            logger.error(f"Phase 4 failed: {e}")
            return False
    
    def run_phase5(self) -> bool:
        """
        Run Phase 5: Resistance Mechanism Analysis
        
        Returns:
            Success status
        """
        logger.info("=" * 60)
        logger.info("PHASE 5: RESISTANCE MECHANISM ANALYSIS")
        logger.info("=" * 60)
        
        try:
            analyzer = ResistanceAnalyzer(
                data_dir=f"{self.config['results_dir']}/annotation",
                results_dir=f"{self.config['results_dir']}/resistance"
            )
            
            # Load annotated data
            adata = analyzer.load_annotated_data()
            
            # Identify timepoints
            adata = analyzer.identify_timepoints(adata)
            
            # Perform resistance analyses
            antigen_results = analyzer.antigen_escape_analysis(adata)
            analyzer.save_results(antigen_results, "antigen_escape")
            
            dysfunction_results = analyzer.cart_dysfunction_analysis(adata)
            analyzer.save_results(dysfunction_results, "cart_dysfunction")
            
            microenv_results = analyzer.microenvironment_analysis(adata)
            analyzer.save_results(microenv_results, "microenvironment")
            
            trajectory_results = analyzer.trajectory_analysis(adata)
            analyzer.save_results(trajectory_results, "trajectory")
            
            # Save final data
            output_path = analyzer.results_dir / "resistance_analyzed_data.h5ad"
            adata.write(output_path)
            
            logger.info("Phase 5 completed: Resistance mechanism analysis successful")
            return True
            
        except Exception as e:
            logger.error(f"Phase 5 failed: {e}")
            return False
    
    def run_pipeline(self, phases: Optional[List[int]] = None) -> bool:
        """
        Run the complete pipeline or specified phases
        
        Args:
            phases: List of phase numbers to run (default: all phases)
            
        Returns:
            Success status
        """
        if phases is None:
            phases = self.config.get('phases_to_run', [1, 2, 3, 4, 5])
        
        logger.info("Starting BCMA CAR-T Resistance Analysis Pipeline")
        logger.info(f"Phases to run: {phases}")
        
        phase_functions = {
            1: self.run_phase1,
            2: self.run_phase2,
            3: self.run_phase3,
            4: self.run_phase4,
            5: self.run_phase5
        }
        
        success_count = 0
        for phase in phases:
            if phase in phase_functions:
                success = phase_functions[phase]()
                if success:
                    success_count += 1
                    logger.info(f"✓ Phase {phase} completed successfully")
                else:
                    logger.error(f"✗ Phase {phase} failed")
                    if self.config.get('stop_on_failure', True):
                        logger.error("Stopping pipeline due to phase failure")
                        break
            else:
                logger.warning(f"Unknown phase: {phase}")
        
        # Generate final summary
        self.generate_summary(success_count, len(phases))
        
        return success_count == len(phases)
    
    def generate_summary(self, successful_phases: int, total_phases: int):
        """
        Generate pipeline execution summary
        
        Args:
            successful_phases: Number of successful phases
            total_phases: Total number of phases attempted
        """
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        summary = {
            'pipeline_name': 'BCMA CAR-T Resistance Analysis',
            'start_time': self.start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'successful_phases': successful_phases,
            'total_phases': total_phases,
            'success_rate': successful_phases / total_phases if total_phases > 0 else 0,
            'config': self.config
        }
        
        # Save summary
        summary_file = self.results_dir / "pipeline_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        logger.info("=" * 60)
        logger.info("PIPELINE EXECUTION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Duration: {duration}")
        logger.info(f"Successful phases: {successful_phases}/{total_phases}")
        logger.info(f"Success rate: {summary['success_rate']:.1%}")
        logger.info(f"Summary saved to: {summary_file}")

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='BCMA CAR-T Resistance Analysis Pipeline')
    parser.add_argument('--config', type=str, help='Configuration file path')
    parser.add_argument('--phases', type=int, nargs='+', choices=[1,2,3,4,5], 
                       help='Specific phases to run (default: all)')
    parser.add_argument('--log-level', type=str, default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    # Initialize and run pipeline
    pipeline = BCMAPipeline(args.config)
    success = pipeline.run_pipeline(args.phases)
    
    if success:
        logger.info("🎉 Pipeline completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Pipeline failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
