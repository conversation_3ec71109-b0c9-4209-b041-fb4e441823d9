# BCMA CAR-T Therapy Resistance Analysis Pipeline

A comprehensive single-cell RNA sequencing (scRNA-seq) analysis pipeline for studying BCMA CAR-T therapy resistance mechanisms in multiple myeloma patients.

## Overview

This pipeline provides a complete workflow for analyzing longitudinal scRNA-seq data from BCMA CAR-T therapy studies, focusing on identifying and characterizing resistance mechanisms. The analysis is structured in five main phases:

1. **Data Collection and Inventory** - Automated download and organization of public datasets
2. **Single-Cell Quality Control** - Comprehensive QC with doublet detection and normalization
3. **Batch Integration** - Advanced batch effect correction using Harmony and scVI
4. **Cell Type Annotation** - Multi-method cell type identification and validation
5. **Resistance Mechanism Analysis** - In-depth analysis of resistance pathways and mechanisms

## Key Features

- **Automated Data Acquisition**: Downloads and processes multiple GEO datasets
- **Robust Quality Control**: SCTransform normalization, doublet detection with scrublet
- **Advanced Integration**: Harmony and scVI/scANVI for batch correction
- **Comprehensive Annotation**: CellTypist, reference-based, and manual annotation
- **Resistance Analysis**: Antigen escape, CAR-T dysfunction, and microenvironment analysis
- **Trajectory Analysis**: scVelo and Monocle3 for temporal dynamics
- **Cell Communication**: CellChat and NicheNet for interaction analysis
- **Reproducible Workflow**: Configurable parameters and comprehensive logging

## Installation

### Prerequisites

- Python 3.8 or higher
- R 4.0 or higher (for some optional analyses)
- Git
- wget or curl for data download

### Python Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd BCMA-CAR-T

# Install Python dependencies
pip install -r requirements.txt

# Optional: Install additional packages for enhanced functionality
pip install celltypist scvi-tools scirpy gseapy
```

### System Dependencies

For data download:
```bash
# Ubuntu/Debian
sudo apt-get install wget curl

# macOS
brew install wget curl

# Windows
# Install wget and curl through package managers or download binaries
```

## Quick Start

### 1. Basic Pipeline Execution

Run the complete pipeline with default settings:

```bash
python main_pipeline.py
```

### 2. Custom Configuration

Create a custom configuration file or modify `config.json`:

```bash
python main_pipeline.py --config my_config.json
```

### 3. Run Specific Phases

Run only specific phases of the pipeline:

```bash
# Run only data collection and QC
python main_pipeline.py --phases 1 2

# Run only resistance analysis
python main_pipeline.py --phases 5
```

### 4. Individual Phase Execution

Run individual phases separately:

```bash
# Phase 1: Data Collection
python phase1_data_collection.py

# Phase 2: Quality Control
python phase2_quality_control.py

# Phase 3: Batch Integration
python phase3_batch_integration.py

# Phase 4: Cell Type Annotation
python phase4_cell_annotation.py

# Phase 5: Resistance Analysis
python phase5_resistance_analysis.py
```

## Pipeline Phases

### Phase 1: Data Collection and Inventory

**Objective**: Compile publicly available longitudinal scRNA-seq datasets focusing on BCMA CAR-T therapy.

**Key Datasets**:
- GSE210079: BCMA CAR-T therapy in multiple myeloma
- GSE164551: Single-cell analysis of CAR-T therapy
- GSE246342: Longitudinal CAR-T therapy response
- GSE274185: BCMA CAR-T resistance mechanisms
- GSE143317: Multiple myeloma CAR-T treatment

**Features**:
- Automated GEO dataset download using GEOquery and SRA-Toolkit
- Data format standardization and metadata extraction
- Quality assessment and inventory generation

### Phase 2: Single-Cell Quality Control

**Objective**: Implement comprehensive quality control to remove dead cells, doublets, and low-quality cells.

**Methods**:
- **Normalization**: SCTransform-like normalization
- **Doublet Detection**: scrublet for automated doublet identification
- **Quality Metrics**: Gene count, mitochondrial percentage, ribosomal content
- **Filtering**: Configurable thresholds for cell and gene filtering

**Outputs**:
- Quality-controlled datasets
- QC metrics and visualizations
- Filtering statistics and reports

### Phase 3: Batch Integration

**Objective**: Eliminate technical batch effects while preserving biological heterogeneity.

**Methods**:
- **Harmony**: Linear batch correction method
- **scVI/scANVI**: Deep learning-based integration
- **Quality Assessment**: LISI scores, silhouette analysis, mixing entropy

**Outputs**:
- Integrated datasets with corrected embeddings
- Integration quality metrics
- Comparative visualizations

### Phase 4: Cell Type Annotation

**Objective**: Identify and annotate major cell populations with high accuracy.

**Cell Types Identified**:
- Malignant plasma cells
- CAR-T cells (CD4+ and CD8+ subsets)
- Endogenous immune cells (T, B, NK, myeloid)
- Bone marrow stromal cells
- Endothelial cells

**Methods**:
- **Reference-based**: Blueprint and Human Cell Atlas references
- **Automated**: CellTypist for immune cell classification
- **Manual**: Marker gene-based annotation
- **Consensus**: Integration of multiple annotation methods

### Phase 5: Resistance Mechanism Analysis

**Objective**: Investigate three primary resistance mechanisms in detail.

#### 1. Antigen Escape Analysis
- BCMA expression changes over time
- Alternative target antigen identification
- Antigen processing pathway analysis

#### 2. CAR-T Cell Dysfunction Analysis
- Exhaustion marker assessment (PD-1, CTLA-4, TIGIT, LAG-3)
- Functional gene expression signatures
- CD4+ vs CD8+ CAR-T comparison
- Proliferation and apoptosis analysis

#### 3. Immunosuppressive Microenvironment Analysis
- Regulatory T cell characterization
- Myeloid-derived suppressor cells
- Inhibitory cytokine analysis
- Checkpoint ligand expression

**Additional Analyses**:
- **Trajectory Analysis**: scVelo for RNA velocity and pseudotime
- **Cell Communication**: CellChat for ligand-receptor interactions
- **TCR Diversity**: scRepertoire for clonal analysis
- **Pathway Analysis**: GSEA with MSigDB gene sets

## Configuration

The pipeline is highly configurable through the `config.json` file. Key configuration sections:

### Quality Control Parameters
```json
"quality_control": {
  "min_genes_per_cell": 200,
  "max_genes_per_cell": 5000,
  "max_mito_percent": 20,
  "doublet_threshold": 0.25
}
```

### Integration Methods
```json
"batch_integration": {
  "methods": ["harmony", "scvi"],
  "harmony_params": {
    "theta": [2],
    "sigma": 0.1
  }
}
```

### Annotation Settings
```json
"cell_annotation": {
  "methods": ["manual", "celltypist", "consensus"],
  "confidence_threshold": 0.8
}
```

## Output Structure

```
results/
├── qc/                          # Quality control results
│   ├── plots/                   # QC visualizations
│   ├── *_qc_processed.h5ad      # QC-processed datasets
│   └── *_qc_stats.json          # QC statistics
├── integration/                 # Batch integration results
│   ├── plots/                   # Integration visualizations
│   ├── integrated_data.h5ad     # Integrated dataset
│   └── integration_quality_metrics.json
├── annotation/                  # Cell type annotation results
│   ├── plots/                   # Annotation visualizations
│   ├── annotated_data.h5ad      # Annotated dataset
│   └── annotation_summary.json
├── resistance/                  # Resistance analysis results
│   ├── antigen_escape/          # Antigen escape analysis
│   ├── cart_dysfunction/        # CAR-T dysfunction analysis
│   ├── microenvironment/        # Microenvironment analysis
│   ├── trajectory/              # Trajectory analysis
│   ├── communication/           # Cell-cell communication
│   └── resistance_analyzed_data.h5ad
└── pipeline_summary.json        # Overall pipeline summary
```

## Key Results and Biomarkers

### Resistance Mechanisms Identified

1. **Antigen Escape**
   - BCMA downregulation patterns
   - Alternative target expression (CD38, SLAMF7, GPRC5D)
   - Antigen processing defects

2. **CAR-T Cell Dysfunction**
   - Exhaustion signatures and markers
   - Functional impairment patterns
   - Clonal diversity changes

3. **Immunosuppressive Microenvironment**
   - Regulatory cell infiltration
   - Inhibitory cytokine networks
   - Metabolic stress signatures

### Therapeutic Targets

- **Primary**: BCMA expression maintenance strategies
- **Alternative**: CD38, SLAMF7, GPRC5D targeting
- **Combination**: Checkpoint inhibition (PD-1, CTLA-4)
- **Microenvironment**: Regulatory T cell depletion

## Troubleshooting

### Common Issues

1. **Data Download Failures**
   ```bash
   # Check internet connection and retry
   python phase1_data_collection.py
   ```

2. **Memory Issues**
   ```bash
   # Reduce memory usage in config.json
   "max_memory_per_process": "8GB"
   ```

3. **Missing Dependencies**
   ```bash
   # Install missing packages
   pip install <missing_package>
   ```

### Log Files

Check log files for detailed error information:
- `pipeline.log` - Main pipeline log
- `data_collection.log` - Data download log
- `quality_control.log` - QC process log
- `batch_integration.log` - Integration log
- `cell_annotation.log` - Annotation log
- `resistance_analysis.log` - Resistance analysis log

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Citation

If you use this pipeline in your research, please cite:

```
BCMA CAR-T Therapy Resistance Analysis Pipeline
[Your publication details here]
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions and support:
- Create an issue on GitHub
- Contact: [<EMAIL>]

## Acknowledgments

- scanpy development team
- scVI-tools contributors
- CellTypist developers
- Harmony algorithm authors
- All dataset contributors and GEO database
