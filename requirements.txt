# BCMA CAR-T scRNA-seq Analysis Pipeline Requirements
# Core scientific computing
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0

# Single-cell analysis
scanpy>=1.8.0
scvi-tools>=0.14.0
anndata>=0.8.0
scvelo>=0.2.4
cellrank>=1.5.0

# Bioinformatics
biopython>=1.79
pysam>=0.17.0
pyranges>=0.0.111

# Machine learning
scikit-learn>=1.0.0
umap-learn>=0.5.0
leiden>=0.3.9
louvain>=0.7.0

# Statistical analysis
statsmodels>=0.12.0
pingouin>=0.4.0

# Data handling
h5py>=3.3.0
tables>=3.6.0
zarr>=2.8.0
loompy>=3.0.6

# Visualization
bokeh>=2.3.0
holoviews>=1.14.0
datashader>=0.13.0

# Trajectory analysis
palantir>=1.0.0
scFates>=0.1.0

# Cell type annotation
celltypist>=1.0.0

# TCR analysis
scirpy>=0.10.0

# Cell-cell communication
cellphonedb>=2.1.0
omnipath>=1.0.0

# Pathway analysis
gseapy>=0.10.0
decoupler>=1.0.0

# Batch correction
harmonypy>=0.0.5
bbknn>=1.5.0

# Quality control
scrublet>=0.2.3

# Utilities
tqdm>=4.62.0
joblib>=1.0.0
psutil>=5.8.0
requests>=2.25.0
lxml>=4.6.0

# Jupyter and documentation
jupyter>=1.0.0
notebook>=6.4.0
ipywidgets>=7.6.0
nbformat>=5.1.0

# Development and testing
pytest>=6.2.0
black>=21.0.0
flake8>=3.9.0

# R integration (optional)
rpy2>=3.4.0

# GPU acceleration (optional)
# cupy-cuda11x>=9.0.0  # Uncomment if CUDA 11.x available
# cudf>=21.0.0         # Uncomment for GPU-accelerated pandas
# cuml>=21.0.0         # Uncomment for GPU-accelerated ML
