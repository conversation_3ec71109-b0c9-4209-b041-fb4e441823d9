#!/usr/bin/env python3
"""
Phase 2: Single-Cell Quality Control
BCMA CAR-T Therapy Resistance Analysis Pipeline

This module implements comprehensive quality control for single-cell RNA-seq data:
- Dead cell and low-quality cell removal
- Doublet detection using scrublet
- SCTransform-like normalization
- Filtering based on gene count, mitochondrial percentage, and ribosomal content

Author: BCMA CAR-T Analysis Pipeline
Date: 2025-06-29
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import scanpy as sc
import anndata as ad
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
import scrublet as scr
from typing import Dict, List, Tuple, Optional, Union
import warnings
from scipy import sparse
from sklearn.preprocessing import StandardScaler
import json

# Configure scanpy settings
sc.settings.verbosity = 3
sc.settings.set_figure_params(dpi=80, facecolor='white')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('quality_control.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class QualityController:
    """
    Main class for single-cell RNA-seq quality control
    """
    
    def __init__(self, data_dir: str = "data", results_dir: str = "results/qc"):
        """
        Initialize the QualityController
        
        Args:
            data_dir: Directory containing raw data
            results_dir: Directory for QC results
        """
        self.data_dir = Path(data_dir)
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # QC parameters
        self.qc_params = {
            'min_genes_per_cell': 200,
            'max_genes_per_cell': 5000,
            'min_cells_per_gene': 3,
            'max_mito_percent': 20,
            'max_ribo_percent': 50,
            'doublet_threshold': 0.25,
            'min_counts_per_cell': 1000,
            'max_counts_per_cell': 30000
        }
        
        logger.info(f"QualityController initialized")
        logger.info(f"Data directory: {self.data_dir}")
        logger.info(f"Results directory: {self.results_dir}")
    
    def load_dataset(self, dataset_path: Union[str, Path]) -> ad.AnnData:
        """
        Load a single-cell dataset
        
        Args:
            dataset_path: Path to the dataset
            
        Returns:
            AnnData object
        """
        dataset_path = Path(dataset_path)
        
        if dataset_path.suffix == '.h5ad':
            adata = sc.read_h5ad(dataset_path)
        elif dataset_path.suffix == '.h5':
            adata = sc.read_10x_h5(dataset_path)
        elif dataset_path.name == 'matrix.mtx' or dataset_path.suffix == '.mtx':
            adata = sc.read_10x_mtx(dataset_path.parent)
        else:
            # Try to read as CSV/TSV
            try:
                df = pd.read_csv(dataset_path, index_col=0)
                adata = ad.AnnData(df.T)  # Transpose so cells are observations
            except:
                raise ValueError(f"Cannot read dataset from {dataset_path}")
        
        # Make variable names unique
        adata.var_names_unique()
        
        logger.info(f"Loaded dataset: {adata.n_obs} cells, {adata.n_vars} genes")
        return adata
    
    def calculate_qc_metrics(self, adata: ad.AnnData) -> ad.AnnData:
        """
        Calculate quality control metrics
        
        Args:
            adata: AnnData object
            
        Returns:
            AnnData object with QC metrics
        """
        logger.info("Calculating QC metrics")
        
        # Make a copy to avoid modifying original
        adata = adata.copy()
        
        # Identify mitochondrial genes
        adata.var['mt'] = adata.var_names.str.startswith('MT-')
        
        # Identify ribosomal genes
        adata.var['ribo'] = adata.var_names.str.startswith(('RPS', 'RPL'))
        
        # Identify hemoglobin genes (often problematic)
        adata.var['hb'] = adata.var_names.str.contains('^HB[^(P)]')
        
        # Calculate QC metrics
        sc.pp.calculate_qc_metrics(
            adata, 
            percent_top=None, 
            log1p=False, 
            inplace=True,
            var_type='genes'
        )
        
        # Calculate mitochondrial gene percentage
        sc.pp.calculate_qc_metrics(
            adata, 
            qc_vars=['mt'], 
            percent_top=None, 
            log1p=False, 
            inplace=True
        )
        
        # Calculate ribosomal gene percentage
        sc.pp.calculate_qc_metrics(
            adata, 
            qc_vars=['ribo'], 
            percent_top=None, 
            log1p=False, 
            inplace=True
        )
        
        # Calculate hemoglobin gene percentage
        sc.pp.calculate_qc_metrics(
            adata, 
            qc_vars=['hb'], 
            percent_top=None, 
            log1p=False, 
            inplace=True
        )
        
        # Add additional metrics
        adata.obs['log10_total_counts'] = np.log10(adata.obs['total_counts'])
        adata.obs['log10_n_genes_by_counts'] = np.log10(adata.obs['n_genes_by_counts'])
        
        logger.info("QC metrics calculated")
        return adata
    
    def detect_doublets(self, adata: ad.AnnData, expected_doublet_rate: float = 0.06) -> ad.AnnData:
        """
        Detect doublets using scrublet
        
        Args:
            adata: AnnData object
            expected_doublet_rate: Expected doublet rate
            
        Returns:
            AnnData object with doublet predictions
        """
        logger.info("Detecting doublets with scrublet")
        
        # Initialize scrublet
        scrub = scr.Scrublet(
            adata.X, 
            expected_doublet_rate=expected_doublet_rate
        )
        
        # Run doublet detection
        doublet_scores, predicted_doublets = scrub.scrub_doublets(
            min_counts=2,
            min_cells=3,
            min_gene_variability_pctl=85,
            n_prin_comps=30
        )
        
        # Add results to adata
        adata.obs['doublet_score'] = doublet_scores
        adata.obs['predicted_doublet'] = predicted_doublets
        
        # Calculate doublet threshold
        doublet_threshold = scrub.threshold_
        adata.uns['doublet_threshold'] = doublet_threshold
        
        n_doublets = np.sum(predicted_doublets)
        doublet_rate = n_doublets / adata.n_obs
        
        logger.info(f"Detected {n_doublets} doublets ({doublet_rate:.2%} of cells)")
        logger.info(f"Doublet threshold: {doublet_threshold:.3f}")
        
        return adata
    
    def apply_quality_filters(self, adata: ad.AnnData) -> Tuple[ad.AnnData, Dict]:
        """
        Apply quality control filters
        
        Args:
            adata: AnnData object with QC metrics
            
        Returns:
            Filtered AnnData object and filtering statistics
        """
        logger.info("Applying quality control filters")
        
        # Store original counts
        n_cells_orig = adata.n_obs
        n_genes_orig = adata.n_vars
        
        # Create filter masks
        filters = {}
        
        # Gene filters
        filters['min_cells_per_gene'] = adata.var['n_cells_by_counts'] >= self.qc_params['min_cells_per_gene']
        
        # Cell filters
        filters['min_genes'] = adata.obs['n_genes_by_counts'] >= self.qc_params['min_genes_per_cell']
        filters['max_genes'] = adata.obs['n_genes_by_counts'] <= self.qc_params['max_genes_per_cell']
        filters['min_counts'] = adata.obs['total_counts'] >= self.qc_params['min_counts_per_cell']
        filters['max_counts'] = adata.obs['total_counts'] <= self.qc_params['max_counts_per_cell']
        filters['max_mito'] = adata.obs['pct_counts_mt'] <= self.qc_params['max_mito_percent']
        filters['max_ribo'] = adata.obs['pct_counts_ribo'] <= self.qc_params['max_ribo_percent']
        filters['not_doublet'] = ~adata.obs['predicted_doublet']
        
        # Combine cell filters
        cell_filter = (
            filters['min_genes'] & 
            filters['max_genes'] & 
            filters['min_counts'] & 
            filters['max_counts'] & 
            filters['max_mito'] & 
            filters['max_ribo'] & 
            filters['not_doublet']
        )
        
        # Apply filters
        adata_filtered = adata[cell_filter, filters['min_cells_per_gene']].copy()
        
        # Calculate filtering statistics
        stats = {
            'original_cells': n_cells_orig,
            'original_genes': n_genes_orig,
            'filtered_cells': adata_filtered.n_obs,
            'filtered_genes': adata_filtered.n_vars,
            'cells_removed': n_cells_orig - adata_filtered.n_obs,
            'genes_removed': n_genes_orig - adata_filtered.n_vars,
            'cell_retention_rate': adata_filtered.n_obs / n_cells_orig,
            'gene_retention_rate': adata_filtered.n_vars / n_genes_orig
        }
        
        # Add filter details
        for filter_name, filter_mask in filters.items():
            if filter_name.startswith('min_cells'):
                stats[f'{filter_name}_removed'] = n_genes_orig - np.sum(filter_mask)
            else:
                stats[f'{filter_name}_removed'] = n_cells_orig - np.sum(filter_mask)
        
        logger.info(f"Filtering completed:")
        logger.info(f"  Cells: {n_cells_orig} → {adata_filtered.n_obs} ({stats['cell_retention_rate']:.2%} retained)")
        logger.info(f"  Genes: {n_genes_orig} → {adata_filtered.n_vars} ({stats['gene_retention_rate']:.2%} retained)")
        
        return adata_filtered, stats
    
    def sctransform_normalization(self, adata: ad.AnnData) -> ad.AnnData:
        """
        Apply SCTransform-like normalization
        
        Args:
            adata: AnnData object
            
        Returns:
            Normalized AnnData object
        """
        logger.info("Applying SCTransform-like normalization")
        
        # Store raw counts
        adata.raw = adata
        
        # Log-normalize to 10,000 reads per cell
        sc.pp.normalize_total(adata, target_sum=1e4)
        sc.pp.log1p(adata)
        
        # Find highly variable genes
        sc.pp.highly_variable_genes(
            adata, 
            min_mean=0.0125, 
            max_mean=3, 
            min_disp=0.5,
            batch_key=None  # Will be updated when batch info is available
        )
        
        # Keep only highly variable genes for downstream analysis
        adata.var['highly_variable_original'] = adata.var['highly_variable'].copy()
        
        logger.info(f"Found {np.sum(adata.var['highly_variable'])} highly variable genes")
        
        return adata
    
    def generate_qc_plots(self, adata: ad.AnnData, sample_name: str = "sample"):
        """
        Generate comprehensive QC plots
        
        Args:
            adata: AnnData object with QC metrics
            sample_name: Name of the sample for plot titles
        """
        logger.info(f"Generating QC plots for {sample_name}")
        
        # Create figure directory
        plot_dir = self.results_dir / "plots" / sample_name
        plot_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up the plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 1. Basic QC metrics violin plots
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'Quality Control Metrics - {sample_name}', fontsize=16)
        
        # Total counts
        sc.pl.violin(adata, ['total_counts'], jitter=0.4, ax=axes[0,0])
        axes[0,0].set_title('Total Counts per Cell')
        
        # Number of genes
        sc.pl.violin(adata, ['n_genes_by_counts'], jitter=0.4, ax=axes[0,1])
        axes[0,1].set_title('Number of Genes per Cell')
        
        # Mitochondrial percentage
        sc.pl.violin(adata, ['pct_counts_mt'], jitter=0.4, ax=axes[0,2])
        axes[0,2].set_title('Mitochondrial Gene %')
        
        # Ribosomal percentage
        sc.pl.violin(adata, ['pct_counts_ribo'], jitter=0.4, ax=axes[1,0])
        axes[1,0].set_title('Ribosomal Gene %')
        
        # Doublet scores
        sc.pl.violin(adata, ['doublet_score'], jitter=0.4, ax=axes[1,1])
        axes[1,1].set_title('Doublet Scores')
        
        # Hemoglobin percentage
        if 'pct_counts_hb' in adata.obs.columns:
            sc.pl.violin(adata, ['pct_counts_hb'], jitter=0.4, ax=axes[1,2])
            axes[1,2].set_title('Hemoglobin Gene %')
        
        plt.tight_layout()
        plt.savefig(plot_dir / 'qc_violin_plots.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Scatter plots showing relationships
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(f'QC Metric Relationships - {sample_name}', fontsize=16)
        
        # Total counts vs genes
        sc.pl.scatter(adata, x='total_counts', y='n_genes_by_counts', ax=axes[0,0])
        axes[0,0].set_title('Total Counts vs Number of Genes')
        
        # Total counts vs mitochondrial %
        sc.pl.scatter(adata, x='total_counts', y='pct_counts_mt', ax=axes[0,1])
        axes[0,1].set_title('Total Counts vs Mitochondrial %')
        
        # Genes vs mitochondrial %
        sc.pl.scatter(adata, x='n_genes_by_counts', y='pct_counts_mt', ax=axes[1,0])
        axes[1,0].set_title('Number of Genes vs Mitochondrial %')
        
        # Doublet score vs total counts
        sc.pl.scatter(adata, x='total_counts', y='doublet_score', ax=axes[1,1])
        axes[1,1].set_title('Total Counts vs Doublet Score')
        
        plt.tight_layout()
        plt.savefig(plot_dir / 'qc_scatter_plots.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Doublet detection plot
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # Doublet score histogram
        axes[0].hist(adata.obs['doublet_score'], bins=50, alpha=0.7, edgecolor='black')
        if 'doublet_threshold' in adata.uns:
            axes[0].axvline(adata.uns['doublet_threshold'], color='red', linestyle='--', 
                           label=f"Threshold: {adata.uns['doublet_threshold']:.3f}")
        axes[0].set_xlabel('Doublet Score')
        axes[0].set_ylabel('Number of Cells')
        axes[0].set_title('Doublet Score Distribution')
        axes[0].legend()
        
        # Doublet prediction
        doublet_counts = adata.obs['predicted_doublet'].value_counts()
        axes[1].pie(doublet_counts.values, labels=['Singlet', 'Doublet'], autopct='%1.1f%%')
        axes[1].set_title('Doublet Prediction')
        
        plt.tight_layout()
        plt.savefig(plot_dir / 'doublet_detection.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"QC plots saved to {plot_dir}")
    
    def process_dataset(self, dataset_path: Union[str, Path], sample_name: str = None) -> Tuple[ad.AnnData, Dict]:
        """
        Process a single dataset through the complete QC pipeline
        
        Args:
            dataset_path: Path to the dataset
            sample_name: Name of the sample
            
        Returns:
            Processed AnnData object and QC statistics
        """
        if sample_name is None:
            sample_name = Path(dataset_path).stem
        
        logger.info(f"Processing dataset: {sample_name}")
        
        # Load dataset
        adata = self.load_dataset(dataset_path)
        
        # Calculate QC metrics
        adata = self.calculate_qc_metrics(adata)
        
        # Detect doublets
        adata = self.detect_doublets(adata)
        
        # Generate QC plots (before filtering)
        self.generate_qc_plots(adata, f"{sample_name}_before_filtering")
        
        # Apply quality filters
        adata_filtered, filter_stats = self.apply_quality_filters(adata)
        
        # Generate QC plots (after filtering)
        self.generate_qc_plots(adata_filtered, f"{sample_name}_after_filtering")
        
        # Apply normalization
        adata_normalized = self.sctransform_normalization(adata_filtered)
        
        # Save processed data
        output_path = self.results_dir / f"{sample_name}_qc_processed.h5ad"
        adata_normalized.write(output_path)
        
        # Save QC statistics
        stats_path = self.results_dir / f"{sample_name}_qc_stats.json"
        with open(stats_path, 'w') as f:
            json.dump(filter_stats, f, indent=2)
        
        logger.info(f"Processed dataset saved to {output_path}")
        logger.info(f"QC statistics saved to {stats_path}")
        
        return adata_normalized, filter_stats

def main():
    """Main execution function"""
    logger.info("Starting Phase 2: Quality Control")
    
    # Initialize quality controller
    qc = QualityController()
    
    # Example usage - process datasets from Phase 1
    data_dir = Path("data/raw")
    
    if data_dir.exists():
        # Find all datasets
        datasets = list(data_dir.glob("GSE*"))
        
        for dataset_dir in datasets:
            geo_id = dataset_dir.name
            logger.info(f"Processing {geo_id}")
            
            # Look for common file formats
            data_files = (
                list(dataset_dir.glob("*.h5ad")) +
                list(dataset_dir.glob("*.h5")) +
                list(dataset_dir.glob("matrix.mtx*")) +
                list(dataset_dir.glob("*.csv*")) +
                list(dataset_dir.glob("*.tsv*"))
            )
            
            if data_files:
                try:
                    adata, stats = qc.process_dataset(data_files[0], geo_id)
                    logger.info(f"✓ Successfully processed {geo_id}")
                except Exception as e:
                    logger.error(f"✗ Failed to process {geo_id}: {e}")
            else:
                logger.warning(f"No suitable data files found in {dataset_dir}")
    else:
        logger.warning("Data directory not found. Please run Phase 1 first.")

if __name__ == "__main__":
    main()
