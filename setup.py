#!/usr/bin/env python3
"""
Setup script for BCMA CAR-T Therapy Resistance Analysis Pipeline

This script sets up the environment and installs dependencies for the pipeline.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    logger.info(f"Python version: {sys.version}")
    return True

def install_requirements():
    """Install Python requirements"""
    logger.info("Installing Python requirements...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("✓ Python requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install requirements: {e}")
        return False

def install_optional_packages():
    """Install optional packages for enhanced functionality"""
    logger.info("Installing optional packages...")
    
    optional_packages = [
        "celltypist",
        "scvi-tools",
        "scirpy",
        "gseapy",
        "lisi",
        "harmonypy",
        "bbknn"
    ]
    
    failed_packages = []
    
    for package in optional_packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            logger.info(f"✓ Installed {package}")
        except subprocess.CalledProcessError:
            logger.warning(f"✗ Failed to install {package} (optional)")
            failed_packages.append(package)
    
    if failed_packages:
        logger.warning(f"Failed to install optional packages: {failed_packages}")
        logger.warning("Some functionality may be limited")
    
    return True

def check_system_dependencies():
    """Check for required system dependencies"""
    logger.info("Checking system dependencies...")
    
    dependencies = {
        "wget": "wget --version",
        "curl": "curl --version"
    }
    
    missing_deps = []
    
    for tool, command in dependencies.items():
        try:
            subprocess.run(command.split(), capture_output=True, check=True)
            logger.info(f"✓ {tool} is available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            missing_deps.append(tool)
            logger.warning(f"✗ {tool} is not available")
    
    if missing_deps:
        logger.warning(f"Missing system dependencies: {missing_deps}")
        logger.warning("Please install them using your system package manager:")
        logger.warning("  Ubuntu/Debian: sudo apt-get install wget curl")
        logger.warning("  macOS: brew install wget curl")
        logger.warning("  Windows: Install through package managers or download binaries")
    
    return len(missing_deps) == 0

def create_directories():
    """Create necessary directories"""
    logger.info("Creating directory structure...")
    
    directories = [
        "data",
        "data/raw",
        "data/processed",
        "data/metadata",
        "results",
        "results/qc",
        "results/integration", 
        "results/annotation",
        "results/resistance",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✓ Created directory: {directory}")
    
    return True

def setup_git_hooks():
    """Setup git hooks for development (optional)"""
    if not Path(".git").exists():
        logger.info("Not a git repository, skipping git hooks setup")
        return True
    
    logger.info("Setting up git hooks...")
    
    # Create pre-commit hook for code formatting
    pre_commit_hook = """#!/bin/bash
# Pre-commit hook for code formatting
echo "Running pre-commit checks..."

# Check Python syntax
python -m py_compile *.py
if [ $? -ne 0 ]; then
    echo "Python syntax errors found. Commit aborted."
    exit 1
fi

echo "Pre-commit checks passed."
"""
    
    hooks_dir = Path(".git/hooks")
    hooks_dir.mkdir(exist_ok=True)
    
    pre_commit_file = hooks_dir / "pre-commit"
    with open(pre_commit_file, 'w') as f:
        f.write(pre_commit_hook)
    
    # Make executable
    os.chmod(pre_commit_file, 0o755)
    
    logger.info("✓ Git hooks setup completed")
    return True

def download_test_data():
    """Download small test dataset for pipeline validation"""
    logger.info("Downloading test data...")
    
    # This would download a small test dataset
    # For now, we'll create a placeholder
    test_data_dir = Path("data/test")
    test_data_dir.mkdir(exist_ok=True)
    
    # Create a simple test metadata file
    test_metadata = {
        "test_dataset": "Small test dataset for pipeline validation",
        "n_cells": 1000,
        "n_genes": 2000,
        "format": "h5ad"
    }
    
    import json
    with open(test_data_dir / "test_metadata.json", 'w') as f:
        json.dump(test_metadata, f, indent=2)
    
    logger.info("✓ Test data setup completed")
    return True

def validate_installation():
    """Validate the installation by importing key modules"""
    logger.info("Validating installation...")
    
    required_modules = [
        "numpy",
        "pandas", 
        "scanpy",
        "anndata",
        "matplotlib",
        "seaborn",
        "scipy",
        "sklearn"
    ]
    
    optional_modules = [
        "celltypist",
        "scvi",
        "scirpy",
        "gseapy",
        "harmonypy"
    ]
    
    # Test required modules
    failed_required = []
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✓ {module} imported successfully")
        except ImportError:
            failed_required.append(module)
            logger.error(f"✗ Failed to import {module}")
    
    # Test optional modules
    failed_optional = []
    for module in optional_modules:
        try:
            __import__(module)
            logger.info(f"✓ {module} imported successfully")
        except ImportError:
            failed_optional.append(module)
            logger.warning(f"✗ Failed to import {module} (optional)")
    
    if failed_required:
        logger.error(f"Required modules failed: {failed_required}")
        return False
    
    if failed_optional:
        logger.warning(f"Optional modules failed: {failed_optional}")
        logger.warning("Some functionality may be limited")
    
    logger.info("✓ Installation validation completed")
    return True

def main():
    """Main setup function"""
    logger.info("=" * 60)
    logger.info("BCMA CAR-T Pipeline Setup")
    logger.info("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        logger.error("Failed to create directories")
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        logger.error("Failed to install requirements")
        sys.exit(1)
    
    # Install optional packages
    install_optional_packages()
    
    # Check system dependencies
    check_system_dependencies()
    
    # Setup git hooks
    setup_git_hooks()
    
    # Download test data
    download_test_data()
    
    # Validate installation
    if not validate_installation():
        logger.error("Installation validation failed")
        sys.exit(1)
    
    logger.info("=" * 60)
    logger.info("Setup completed successfully!")
    logger.info("=" * 60)
    logger.info("Next steps:")
    logger.info("1. Review and modify config.json as needed")
    logger.info("2. Run the pipeline: python main_pipeline.py")
    logger.info("3. Check the README.md for detailed usage instructions")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
