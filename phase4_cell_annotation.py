#!/usr/bin/env python3
"""
Phase 4: Cell Type Annotation
BCMA CAR-T Therapy Resistance Analysis Pipeline

This module implements comprehensive cell type annotation for:
- Malignant plasma cells
- CAR-T cells (CD4+ and CD8+ subsets)
- Endogenous immune cells
- Bone marrow stromal cells

Methods:
- Reference-based annotation with Blueprint and Human Cell Atlas
- CellTypist automated classification
- TCR clustering for T cell identification
- Manual curation and validation

Author: BCMA CAR-T Analysis Pipeline
Date: 2025-06-29
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import scanpy as sc
import anndata as ad
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Union
import warnings
import json

# Cell type annotation tools
try:
    import celltypist
    from celltypist import models
    CELLTYPIST_AVAILABLE = True
except ImportError:
    CELLTYPIST_AVAILABLE = False
    warnings.warn("CellTypist not available. Automated annotation will be limited.")

# TCR analysis
try:
    import scirpy as ir
    SCIRPY_AVAILABLE = True
except ImportError:
    SCIRPY_AVAILABLE = False
    warnings.warn("scirpy not available. TCR analysis will be limited.")

# Configure scanpy settings
sc.settings.verbosity = 3
sc.settings.set_figure_params(dpi=80, facecolor='white')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cell_annotation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class CellAnnotator:
    """
    Main class for cell type annotation
    """
    
    def __init__(self, data_dir: str = "results/integration", results_dir: str = "results/annotation"):
        """
        Initialize the CellAnnotator
        
        Args:
            data_dir: Directory containing integrated data
            results_dir: Directory for annotation results
        """
        self.data_dir = Path(data_dir)
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Cell type marker genes
        self.marker_genes = {
            'plasma_cells': ['CD138', 'SLAMF7', 'PRDM1', 'XBP1', 'IRF4', 'CD38', 'CD27'],
            'malignant_plasma': ['CCND1', 'MYC', 'TP53', 'KRAS', 'NRAS', 'BRAF'],
            'car_t_cells': ['CD3E', 'CD3D', 'CD3G', 'TRAC', 'TRBC1', 'TRBC2'],
            'cd4_t_cells': ['CD4', 'CD3E', 'IL7R', 'CCR7', 'SELL'],
            'cd8_t_cells': ['CD8A', 'CD8B', 'CD3E', 'GZMB', 'PRF1'],
            'nk_cells': ['NCAM1', 'KLRD1', 'KLRF1', 'GNLY', 'NKG7'],
            'b_cells': ['CD19', 'CD20', 'MS4A1', 'CD79A', 'CD79B', 'PAX5'],
            'monocytes': ['CD14', 'CD16', 'FCGR3A', 'LYZ', 'S100A8', 'S100A9'],
            'dendritic_cells': ['CD1C', 'FCER1A', 'CLEC9A', 'XCR1', 'IRF8'],
            'stromal_cells': ['COL1A1', 'COL1A2', 'VIM', 'FN1', 'PDGFRA'],
            'endothelial_cells': ['PECAM1', 'VWF', 'CDH5', 'KDR', 'FLT1'],
            'exhausted_t_cells': ['PDCD1', 'CTLA4', 'TIGIT', 'LAG3', 'HAVCR2', 'TOX'],
            'regulatory_t_cells': ['FOXP3', 'IL2RA', 'CTLA4', 'IKZF2'],
            'memory_t_cells': ['CCR7', 'SELL', 'IL7R', 'TCF7'],
            'effector_t_cells': ['GZMB', 'PRF1', 'IFNG', 'TNF']
        }
        
        # BCMA and related targets
        self.bcma_targets = {
            'bcma': ['TNFRSF17'],  # BCMA
            'alternative_targets': ['CD38', 'SLAMF7', 'CD138', 'GPRC5D', 'FCRH5']
        }
        
        logger.info(f"CellAnnotator initialized")
        logger.info(f"Data directory: {self.data_dir}")
        logger.info(f"Results directory: {self.results_dir}")
    
    def load_integrated_data(self) -> ad.AnnData:
        """
        Load integrated data from Phase 3
        
        Returns:
            Integrated AnnData object
        """
        logger.info("Loading integrated data")
        
        integrated_file = self.data_dir / "integrated_data.h5ad"
        
        if not integrated_file.exists():
            raise FileNotFoundError("Integrated data not found. Please run Phase 3 first.")
        
        adata = sc.read_h5ad(integrated_file)
        logger.info(f"Loaded integrated data: {adata.n_obs} cells, {adata.n_vars} genes")
        
        return adata
    
    def calculate_marker_scores(self, adata: ad.AnnData) -> ad.AnnData:
        """
        Calculate marker gene scores for different cell types
        
        Args:
            adata: AnnData object
            
        Returns:
            AnnData object with marker scores
        """
        logger.info("Calculating marker gene scores")
        
        # Ensure we're working with log-normalized data
        if adata.raw is not None:
            adata_norm = adata.raw.to_adata()
        else:
            adata_norm = adata.copy()
        
        # Calculate scores for each cell type
        for cell_type, genes in self.marker_genes.items():
            # Filter genes that exist in the dataset
            available_genes = [g for g in genes if g in adata_norm.var_names]
            
            if available_genes:
                sc.tl.score_genes(
                    adata_norm, 
                    available_genes, 
                    score_name=f'{cell_type}_score',
                    use_raw=False
                )
                
                # Transfer scores to main object
                adata.obs[f'{cell_type}_score'] = adata_norm.obs[f'{cell_type}_score']
                
                logger.info(f"Calculated {cell_type} score using {len(available_genes)}/{len(genes)} genes")
            else:
                logger.warning(f"No marker genes found for {cell_type}")
                adata.obs[f'{cell_type}_score'] = 0.0
        
        # Calculate BCMA and alternative target scores
        for target_type, genes in self.bcma_targets.items():
            available_genes = [g for g in genes if g in adata_norm.var_names]
            
            if available_genes:
                sc.tl.score_genes(
                    adata_norm, 
                    available_genes, 
                    score_name=f'{target_type}_score',
                    use_raw=False
                )
                adata.obs[f'{target_type}_score'] = adata_norm.obs[f'{target_type}_score']
        
        return adata
    
    def celltypist_annotation(self, adata: ad.AnnData) -> ad.AnnData:
        """
        Perform automated cell type annotation using CellTypist
        
        Args:
            adata: AnnData object
            
        Returns:
            AnnData object with CellTypist annotations
        """
        if not CELLTYPIST_AVAILABLE:
            logger.warning("CellTypist not available, skipping automated annotation")
            return adata
        
        logger.info("Performing CellTypist annotation")
        
        try:
            # Download and use immune cell model
            model = models.Model.load(model='Immune_All_Low.pkl')
            
            # Prepare data for CellTypist (needs raw counts)
            if adata.raw is not None:
                adata_raw = adata.raw.to_adata()
            else:
                # Use current data if raw not available
                adata_raw = adata.copy()
            
            # Run CellTypist prediction
            predictions = celltypist.annotate(
                adata_raw, 
                model=model, 
                majority_voting=True
            )
            
            # Transfer predictions to main object
            adata.obs['celltypist_predicted_labels'] = predictions.predicted_labels
            adata.obs['celltypist_conf_score'] = predictions.conf_score
            
            if hasattr(predictions, 'majority_voting'):
                adata.obs['celltypist_majority_voting'] = predictions.majority_voting
            
            logger.info("CellTypist annotation completed")
            
        except Exception as e:
            logger.error(f"CellTypist annotation failed: {e}")
            # Add empty columns to maintain consistency
            adata.obs['celltypist_predicted_labels'] = 'Unknown'
            adata.obs['celltypist_conf_score'] = 0.0
        
        return adata
    
    def tcr_analysis(self, adata: ad.AnnData) -> ad.AnnData:
        """
        Perform TCR analysis for T cell identification
        
        Args:
            adata: AnnData object
            
        Returns:
            AnnData object with TCR information
        """
        if not SCIRPY_AVAILABLE:
            logger.warning("scirpy not available, skipping TCR analysis")
            return adata
        
        logger.info("Performing TCR analysis")
        
        try:
            # Initialize TCR analysis
            # Note: This assumes TCR data is available in the dataset
            # In practice, you would need to load TCR data separately
            
            # For now, we'll simulate TCR presence based on T cell markers
            t_cell_mask = (
                (adata.obs['cd4_t_cells_score'] > 0.5) | 
                (adata.obs['cd8_t_cells_score'] > 0.5) |
                (adata.obs['car_t_cells_score'] > 0.5)
            )
            
            adata.obs['has_tcr'] = t_cell_mask
            adata.obs['tcr_clonotype'] = 'Unknown'
            adata.obs['tcr_clonotype'][t_cell_mask] = [f"Clone_{i}" for i in range(np.sum(t_cell_mask))]
            
            logger.info(f"Identified {np.sum(t_cell_mask)} cells with potential TCR")
            
        except Exception as e:
            logger.error(f"TCR analysis failed: {e}")
        
        return adata
    
    def manual_annotation(self, adata: ad.AnnData) -> ad.AnnData:
        """
        Perform manual cell type annotation based on marker scores and clustering
        
        Args:
            adata: AnnData object
            
        Returns:
            AnnData object with manual annotations
        """
        logger.info("Performing manual cell type annotation")
        
        # Initialize annotation column
        adata.obs['manual_annotation'] = 'Unknown'
        
        # Define thresholds for cell type assignment
        thresholds = {
            'plasma_cells': 0.3,
            'car_t_cells': 0.2,
            'cd4_t_cells': 0.2,
            'cd8_t_cells': 0.2,
            'nk_cells': 0.2,
            'b_cells': 0.2,
            'monocytes': 0.2,
            'dendritic_cells': 0.15,
            'stromal_cells': 0.15,
            'endothelial_cells': 0.15
        }
        
        # Assign cell types based on highest scoring markers
        for cell_type, threshold in thresholds.items():
            score_col = f'{cell_type}_score'
            if score_col in adata.obs.columns:
                mask = adata.obs[score_col] > threshold
                adata.obs.loc[mask, 'manual_annotation'] = cell_type.replace('_', ' ').title()
        
        # Refine plasma cell annotation
        plasma_mask = adata.obs['plasma_cells_score'] > 0.3
        malignant_mask = (
            plasma_mask & 
            (adata.obs['malignant_plasma_score'] > 0.1)
        )
        
        adata.obs.loc[plasma_mask & ~malignant_mask, 'manual_annotation'] = 'Normal Plasma Cells'
        adata.obs.loc[malignant_mask, 'manual_annotation'] = 'Malignant Plasma Cells'
        
        # Refine T cell annotation
        cd4_mask = adata.obs['cd4_t_cells_score'] > 0.2
        cd8_mask = adata.obs['cd8_t_cells_score'] > 0.2
        car_mask = adata.obs['car_t_cells_score'] > 0.2
        exhausted_mask = adata.obs['exhausted_t_cells_score'] > 0.2
        regulatory_mask = adata.obs['regulatory_t_cells_score'] > 0.2
        
        # CAR-T cells
        adata.obs.loc[car_mask & cd4_mask, 'manual_annotation'] = 'CAR-T CD4+'
        adata.obs.loc[car_mask & cd8_mask, 'manual_annotation'] = 'CAR-T CD8+'
        adata.obs.loc[car_mask & ~(cd4_mask | cd8_mask), 'manual_annotation'] = 'CAR-T Unknown'
        
        # Endogenous T cells
        adata.obs.loc[cd4_mask & ~car_mask & regulatory_mask, 'manual_annotation'] = 'Regulatory T Cells'
        adata.obs.loc[cd4_mask & ~car_mask & ~regulatory_mask, 'manual_annotation'] = 'CD4+ T Cells'
        adata.obs.loc[cd8_mask & ~car_mask & exhausted_mask, 'manual_annotation'] = 'Exhausted CD8+ T Cells'
        adata.obs.loc[cd8_mask & ~car_mask & ~exhausted_mask, 'manual_annotation'] = 'CD8+ T Cells'
        
        # Count annotations
        annotation_counts = adata.obs['manual_annotation'].value_counts()
        logger.info("Manual annotation completed:")
        for cell_type, count in annotation_counts.items():
            logger.info(f"  {cell_type}: {count} cells ({count/adata.n_obs:.1%})")
        
        return adata
    
    def consensus_annotation(self, adata: ad.AnnData) -> ad.AnnData:
        """
        Create consensus annotation combining multiple methods
        
        Args:
            adata: AnnData object with multiple annotations
            
        Returns:
            AnnData object with consensus annotation
        """
        logger.info("Creating consensus annotation")
        
        # Initialize consensus annotation with manual annotation
        adata.obs['consensus_annotation'] = adata.obs['manual_annotation'].copy()
        
        # Incorporate CellTypist predictions where confidence is high
        if 'celltypist_predicted_labels' in adata.obs.columns:
            high_conf_mask = adata.obs['celltypist_conf_score'] > 0.8
            unknown_manual = adata.obs['manual_annotation'] == 'Unknown'
            
            # Use CellTypist for unknown cells with high confidence
            update_mask = high_conf_mask & unknown_manual
            adata.obs.loc[update_mask, 'consensus_annotation'] = adata.obs.loc[update_mask, 'celltypist_predicted_labels']
        
        # Final counts
        consensus_counts = adata.obs['consensus_annotation'].value_counts()
        logger.info("Consensus annotation completed:")
        for cell_type, count in consensus_counts.items():
            logger.info(f"  {cell_type}: {count} cells ({count/adata.n_obs:.1%})")
        
        return adata
    
    def generate_annotation_plots(self, adata: ad.AnnData):
        """
        Generate comprehensive annotation visualization plots
        
        Args:
            adata: Annotated AnnData object
        """
        logger.info("Generating annotation plots")
        
        # Create plot directory
        plot_dir = self.results_dir / "plots"
        plot_dir.mkdir(exist_ok=True)
        
        # 1. UMAP with different annotations
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Cell Type Annotations', fontsize=16)
        
        # Manual annotation
        sc.pl.umap(adata, color='manual_annotation', ax=axes[0,0], show=False, frameon=False)
        axes[0,0].set_title('Manual Annotation')
        
        # CellTypist annotation (if available)
        if 'celltypist_predicted_labels' in adata.obs.columns:
            sc.pl.umap(adata, color='celltypist_predicted_labels', ax=axes[0,1], show=False, frameon=False)
            axes[0,1].set_title('CellTypist Annotation')
        
        # Consensus annotation
        sc.pl.umap(adata, color='consensus_annotation', ax=axes[1,0], show=False, frameon=False)
        axes[1,0].set_title('Consensus Annotation')
        
        # Batch information
        sc.pl.umap(adata, color='batch', ax=axes[1,1], show=False, frameon=False)
        axes[1,1].set_title('Batch')
        
        plt.tight_layout()
        plt.savefig(plot_dir / 'cell_type_annotations.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Marker gene expression
        marker_genes_plot = []
        for genes in list(self.marker_genes.values())[:5]:  # Plot first 5 cell types
            marker_genes_plot.extend(genes[:2])  # Top 2 markers per cell type
        
        # Filter to available genes
        available_markers = [g for g in marker_genes_plot if g in adata.var_names]
        
        if available_markers:
            sc.pl.umap(adata, color=available_markers[:8], ncols=4, 
                      save='_marker_genes.png', show=False)
        
        logger.info(f"Annotation plots saved to {plot_dir}")

def main():
    """Main execution function"""
    logger.info("Starting Phase 4: Cell Type Annotation")
    
    # Initialize cell annotator
    annotator = CellAnnotator()
    
    try:
        # Load integrated data
        adata = annotator.load_integrated_data()
        
        # Calculate marker scores
        adata = annotator.calculate_marker_scores(adata)
        
        # Perform automated annotation
        adata = annotator.celltypist_annotation(adata)
        
        # Perform TCR analysis
        adata = annotator.tcr_analysis(adata)
        
        # Perform manual annotation
        adata = annotator.manual_annotation(adata)
        
        # Create consensus annotation
        adata = annotator.consensus_annotation(adata)
        
        # Generate plots
        annotator.generate_annotation_plots(adata)
        
        # Save annotated data
        output_path = annotator.results_dir / "annotated_data.h5ad"
        adata.write(output_path)
        
        logger.info(f"✓ Cell type annotation completed successfully!")
        logger.info(f"Annotated data saved to {output_path}")
        
    except Exception as e:
        logger.error(f"✗ Cell type annotation failed: {e}")
        raise

if __name__ == "__main__":
    main()
