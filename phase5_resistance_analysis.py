#!/usr/bin/env python3
"""
Phase 5: Resistance Mechanism Analysis
BCMA CAR-T Therapy Resistance Analysis Pipeline

This module investigates three primary resistance mechanisms:
1. Antigen Escape Analysis - BCMA expression changes and alternative targets
2. CAR-T Cell Dysfunction Analysis - Exhaustion markers and functional signatures
3. Immunosuppressive Microenvironment Analysis - Regulatory cells and cytokines

Methods:
- Differential gene expression with FindMarkers and GSEA
- Trajectory analysis with scVelo/Monocle3
- Cell-cell communication with CellChat/NicheNet
- TCR diversity analysis with scRepertoire

Author: BCMA CAR-T Analysis Pipeline
Date: 2025-06-29
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import scanpy as sc
import anndata as ad
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Union
import warnings
import json
from scipy import stats
from sklearn.preprocessing import StandardScaler

# Trajectory analysis
try:
    import scvelo as scv
    SCVELO_AVAILABLE = True
except ImportError:
    SCVELO_AVAILABLE = False
    warnings.warn("scV<PERSON> not available. RNA velocity analysis will be limited.")

# Pathway analysis
try:
    import gseapy as gp
    GSEAPY_AVAILABLE = True
except ImportError:
    GSEAPY_AVAILABLE = False
    warnings.warn("GSEApy not available. Pathway analysis will be limited.")

# Cell-cell communication
try:
    import omnipath as op
    OMNIPATH_AVAILABLE = True
except ImportError:
    OMNIPATH_AVAILABLE = False
    warnings.warn("OmniPath not available. Cell-cell communication analysis will be limited.")

# Configure scanpy settings
sc.settings.verbosity = 3
sc.settings.set_figure_params(dpi=80, facecolor='white')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('resistance_analysis.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ResistanceAnalyzer:
    """
    Main class for resistance mechanism analysis
    """
    
    def __init__(self, data_dir: str = "results/annotation", results_dir: str = "results/resistance"):
        """
        Initialize the ResistanceAnalyzer
        
        Args:
            data_dir: Directory containing annotated data
            results_dir: Directory for resistance analysis results
        """
        self.data_dir = Path(data_dir)
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories for different analyses
        (self.results_dir / "antigen_escape").mkdir(exist_ok=True)
        (self.results_dir / "cart_dysfunction").mkdir(exist_ok=True)
        (self.results_dir / "microenvironment").mkdir(exist_ok=True)
        (self.results_dir / "trajectory").mkdir(exist_ok=True)
        (self.results_dir / "communication").mkdir(exist_ok=True)
        
        # Resistance-related gene sets
        self.resistance_genes = {
            'antigen_escape': {
                'bcma_pathway': ['TNFRSF17', 'TACI', 'BAFF', 'APRIL'],
                'alternative_targets': ['CD38', 'SLAMF7', 'CD138', 'GPRC5D', 'FCRH5'],
                'antigen_processing': ['B2M', 'TAP1', 'TAP2', 'PSMB8', 'PSMB9', 'PSMB10'],
                'mhc_class_i': ['HLA-A', 'HLA-B', 'HLA-C']
            },
            'cart_dysfunction': {
                'exhaustion': ['PDCD1', 'CTLA4', 'TIGIT', 'LAG3', 'HAVCR2', 'TOX', 'ENTPD1'],
                'activation': ['CD69', 'CD25', 'ICOS', 'CD40LG', 'IFNG', 'TNF', 'IL2'],
                'memory': ['CCR7', 'SELL', 'IL7R', 'TCF7', 'LEF1'],
                'effector': ['GZMB', 'PRF1', 'GZMA', 'GNLY', 'NKG7'],
                'proliferation': ['MKI67', 'PCNA', 'TOP2A', 'CDK1'],
                'apoptosis': ['BAX', 'BAK1', 'CASP3', 'CASP8', 'FAS', 'FASLG']
            },
            'immunosuppression': {
                'regulatory_t': ['FOXP3', 'IL2RA', 'CTLA4', 'IKZF2', 'IL10'],
                'myeloid_suppressor': ['ARG1', 'NOS2', 'IDO1', 'PTGS2'],
                'inhibitory_cytokines': ['IL10', 'TGFB1', 'IL6', 'VEGFA'],
                'checkpoint_ligands': ['CD274', 'PDCD1LG2', 'CD80', 'CD86'],
                'metabolic_stress': ['HIF1A', 'LDHA', 'PKM', 'GLUT1']
            }
        }
        
        logger.info(f"ResistanceAnalyzer initialized")
        logger.info(f"Data directory: {self.data_dir}")
        logger.info(f"Results directory: {self.results_dir}")
    
    def load_annotated_data(self) -> ad.AnnData:
        """
        Load annotated data from Phase 4
        
        Returns:
            Annotated AnnData object
        """
        logger.info("Loading annotated data")
        
        annotated_file = self.data_dir / "annotated_data.h5ad"
        
        if not annotated_file.exists():
            raise FileNotFoundError("Annotated data not found. Please run Phase 4 first.")
        
        adata = sc.read_h5ad(annotated_file)
        logger.info(f"Loaded annotated data: {adata.n_obs} cells, {adata.n_vars} genes")
        
        return adata
    
    def identify_timepoints(self, adata: ad.AnnData) -> ad.AnnData:
        """
        Identify and standardize timepoint information
        
        Args:
            adata: AnnData object
            
        Returns:
            AnnData object with standardized timepoint information
        """
        logger.info("Identifying timepoints")
        
        # Try to extract timepoint information from metadata
        # This is dataset-specific and may need adjustment
        
        if 'timepoint' not in adata.obs.columns:
            # Try to infer from sample names or other metadata
            if 'sample' in adata.obs.columns:
                # Simple heuristic based on common naming patterns
                adata.obs['timepoint'] = 'unknown'
                
                sample_names = adata.obs['sample'].astype(str).str.lower()
                adata.obs.loc[sample_names.str.contains('pre|baseline|d0'), 'timepoint'] = 'pre_treatment'
                adata.obs.loc[sample_names.str.contains('post|response|d7|d14|d28'), 'timepoint'] = 'post_treatment'
                adata.obs.loc[sample_names.str.contains('relapse|progression|resistant'), 'timepoint'] = 'relapse'
            else:
                # Default assignment - this should be customized based on actual data
                adata.obs['timepoint'] = 'unknown'
        
        timepoint_counts = adata.obs['timepoint'].value_counts()
        logger.info(f"Timepoint distribution: {timepoint_counts.to_dict()}")
        
        return adata
    
    def antigen_escape_analysis(self, adata: ad.AnnData) -> Dict:
        """
        Analyze antigen escape mechanisms
        
        Args:
            adata: AnnData object
            
        Returns:
            Dictionary with antigen escape analysis results
        """
        logger.info("Performing antigen escape analysis")
        
        results = {}
        
        # Focus on plasma cells
        plasma_cells = adata[adata.obs['consensus_annotation'].str.contains('Plasma', case=False, na=False)]
        
        if plasma_cells.n_obs == 0:
            logger.warning("No plasma cells found for antigen escape analysis")
            return results
        
        # 1. BCMA expression analysis over time
        if 'TNFRSF17' in adata.var_names and 'timepoint' in adata.obs.columns:
            bcma_expression = []
            
            for timepoint in plasma_cells.obs['timepoint'].unique():
                if timepoint != 'unknown':
                    tp_cells = plasma_cells[plasma_cells.obs['timepoint'] == timepoint]
                    if tp_cells.n_obs > 0:
                        bcma_expr = tp_cells[:, 'TNFRSF17'].X.toarray().flatten()
                        bcma_expression.append({
                            'timepoint': timepoint,
                            'mean_expression': np.mean(bcma_expr),
                            'median_expression': np.median(bcma_expr),
                            'std_expression': np.std(bcma_expr),
                            'n_cells': len(bcma_expr)
                        })
            
            results['bcma_expression_timeline'] = bcma_expression
        
        # 2. Alternative target expression
        alt_targets = self.resistance_genes['antigen_escape']['alternative_targets']
        available_targets = [g for g in alt_targets if g in adata.var_names]
        
        if available_targets:
            target_expression = {}
            for target in available_targets:
                target_expr = plasma_cells[:, target].X.toarray().flatten()
                target_expression[target] = {
                    'mean': np.mean(target_expr),
                    'median': np.median(target_expr),
                    'positive_cells': np.sum(target_expr > 0) / len(target_expr)
                }
            
            results['alternative_targets'] = target_expression
        
        # 3. Differential expression between timepoints
        if len(plasma_cells.obs['timepoint'].unique()) > 1:
            try:
                # Compare pre-treatment vs relapse if available
                pre_cells = plasma_cells[plasma_cells.obs['timepoint'] == 'pre_treatment']
                relapse_cells = plasma_cells[plasma_cells.obs['timepoint'] == 'relapse']
                
                if pre_cells.n_obs > 10 and relapse_cells.n_obs > 10:
                    # Create comparison dataset
                    comparison_data = ad.concat([pre_cells, relapse_cells])
                    
                    # Perform differential expression
                    sc.tl.rank_genes_groups(
                        comparison_data,
                        groupby='timepoint',
                        groups=['relapse'],
                        reference='pre_treatment',
                        method='wilcoxon',
                        key_added='antigen_escape_de'
                    )
                    
                    # Extract top differentially expressed genes
                    de_results = sc.get.rank_genes_groups_df(
                        comparison_data, 
                        group='relapse',
                        key='antigen_escape_de'
                    )
                    
                    results['differential_expression'] = de_results.head(50).to_dict('records')
                    
            except Exception as e:
                logger.warning(f"Differential expression analysis failed: {e}")
        
        logger.info("Antigen escape analysis completed")
        return results
    
    def cart_dysfunction_analysis(self, adata: ad.AnnData) -> Dict:
        """
        Analyze CAR-T cell dysfunction mechanisms
        
        Args:
            adata: AnnData object
            
        Returns:
            Dictionary with CAR-T dysfunction analysis results
        """
        logger.info("Performing CAR-T cell dysfunction analysis")
        
        results = {}
        
        # Focus on CAR-T cells
        cart_cells = adata[adata.obs['consensus_annotation'].str.contains('CAR-T', case=False, na=False)]
        
        if cart_cells.n_obs == 0:
            logger.warning("No CAR-T cells found for dysfunction analysis")
            return results
        
        # 1. Exhaustion marker analysis
        exhaustion_genes = self.resistance_genes['cart_dysfunction']['exhaustion']
        available_exhaustion = [g for g in exhaustion_genes if g in adata.var_names]
        
        if available_exhaustion:
            # Calculate exhaustion score
            sc.tl.score_genes(cart_cells, available_exhaustion, score_name='exhaustion_score')
            
            # Analyze by timepoint
            if 'timepoint' in cart_cells.obs.columns:
                exhaustion_by_time = []
                for timepoint in cart_cells.obs['timepoint'].unique():
                    if timepoint != 'unknown':
                        tp_cells = cart_cells[cart_cells.obs['timepoint'] == timepoint]
                        if tp_cells.n_obs > 0:
                            exhaustion_by_time.append({
                                'timepoint': timepoint,
                                'mean_exhaustion': np.mean(tp_cells.obs['exhaustion_score']),
                                'median_exhaustion': np.median(tp_cells.obs['exhaustion_score']),
                                'n_cells': tp_cells.n_obs
                            })
                
                results['exhaustion_timeline'] = exhaustion_by_time
        
        # 2. Functional signature analysis
        function_signatures = ['activation', 'effector', 'memory', 'proliferation']
        
        for signature in function_signatures:
            genes = self.resistance_genes['cart_dysfunction'][signature]
            available_genes = [g for g in genes if g in adata.var_names]
            
            if available_genes:
                sc.tl.score_genes(cart_cells, available_genes, score_name=f'{signature}_score')
                
                # Store summary statistics
                results[f'{signature}_signature'] = {
                    'mean': np.mean(cart_cells.obs[f'{signature}_score']),
                    'median': np.median(cart_cells.obs[f'{signature}_score']),
                    'std': np.std(cart_cells.obs[f'{signature}_score'])
                }
        
        # 3. CD4 vs CD8 CAR-T comparison
        cd4_cart = cart_cells[cart_cells.obs['consensus_annotation'].str.contains('CD4', case=False, na=False)]
        cd8_cart = cart_cells[cart_cells.obs['consensus_annotation'].str.contains('CD8', case=False, na=False)]
        
        if cd4_cart.n_obs > 10 and cd8_cart.n_obs > 10:
            # Compare exhaustion between CD4 and CD8 CAR-T cells
            cd4_exhaustion = np.mean(cd4_cart.obs.get('exhaustion_score', [0]))
            cd8_exhaustion = np.mean(cd8_cart.obs.get('exhaustion_score', [0]))
            
            results['cd4_vs_cd8_exhaustion'] = {
                'cd4_exhaustion': cd4_exhaustion,
                'cd8_exhaustion': cd8_exhaustion,
                'cd4_count': cd4_cart.n_obs,
                'cd8_count': cd8_cart.n_obs
            }
        
        logger.info("CAR-T dysfunction analysis completed")
        return results
    
    def microenvironment_analysis(self, adata: ad.AnnData) -> Dict:
        """
        Analyze immunosuppressive microenvironment
        
        Args:
            adata: AnnData object
            
        Returns:
            Dictionary with microenvironment analysis results
        """
        logger.info("Performing microenvironment analysis")
        
        results = {}
        
        # 1. Regulatory cell populations
        reg_t_cells = adata[adata.obs['consensus_annotation'].str.contains('Regulatory', case=False, na=False)]
        
        if reg_t_cells.n_obs > 0:
            results['regulatory_t_cells'] = {
                'count': reg_t_cells.n_obs,
                'percentage': reg_t_cells.n_obs / adata.n_obs * 100
            }
        
        # 2. Immunosuppressive gene signatures
        suppression_signatures = ['regulatory_t', 'myeloid_suppressor', 'inhibitory_cytokines']
        
        for signature in suppression_signatures:
            genes = self.resistance_genes['immunosuppression'][signature]
            available_genes = [g for g in genes if g in adata.var_names]
            
            if available_genes:
                sc.tl.score_genes(adata, available_genes, score_name=f'{signature}_score')
                
                # Analyze by cell type
                cell_type_scores = {}
                for cell_type in adata.obs['consensus_annotation'].unique():
                    if pd.notna(cell_type) and cell_type != 'Unknown':
                        ct_cells = adata[adata.obs['consensus_annotation'] == cell_type]
                        if ct_cells.n_obs > 5:
                            cell_type_scores[cell_type] = np.mean(ct_cells.obs[f'{signature}_score'])
                
                results[f'{signature}_by_celltype'] = cell_type_scores
        
        # 3. Checkpoint ligand expression
        checkpoint_genes = self.resistance_genes['immunosuppression']['checkpoint_ligands']
        available_checkpoints = [g for g in checkpoint_genes if g in adata.var_names]
        
        if available_checkpoints:
            checkpoint_expression = {}
            for gene in available_checkpoints:
                gene_expr = adata[:, gene].X.toarray().flatten()
                checkpoint_expression[gene] = {
                    'mean_expression': np.mean(gene_expr),
                    'positive_cells': np.sum(gene_expr > 0) / len(gene_expr)
                }
            
            results['checkpoint_ligands'] = checkpoint_expression
        
        logger.info("Microenvironment analysis completed")
        return results
    
    def trajectory_analysis(self, adata: ad.AnnData) -> Dict:
        """
        Perform trajectory analysis for resistance development
        
        Args:
            adata: AnnData object
            
        Returns:
            Dictionary with trajectory analysis results
        """
        logger.info("Performing trajectory analysis")
        
        results = {}
        
        if not SCVELO_AVAILABLE:
            logger.warning("scVelo not available, skipping RNA velocity analysis")
            return results
        
        try:
            # Focus on plasma cells for trajectory analysis
            plasma_cells = adata[adata.obs['consensus_annotation'].str.contains('Plasma', case=False, na=False)]
            
            if plasma_cells.n_obs < 50:
                logger.warning("Insufficient plasma cells for trajectory analysis")
                return results
            
            # Prepare for scVelo (requires spliced/unspliced counts)
            # Note: This assumes the data contains spliced/unspliced information
            # In practice, you would need to process the data with velocyto or similar
            
            # For demonstration, we'll use pseudotime analysis instead
            sc.tl.diffmap(plasma_cells)
            sc.tl.dpt(plasma_cells)
            
            # Store pseudotime results
            results['pseudotime_analysis'] = {
                'n_cells': plasma_cells.n_obs,
                'pseudotime_range': [
                    float(np.min(plasma_cells.obs['dpt_pseudotime'])),
                    float(np.max(plasma_cells.obs['dpt_pseudotime']))
                ]
            }
            
            logger.info("Trajectory analysis completed")
            
        except Exception as e:
            logger.error(f"Trajectory analysis failed: {e}")
        
        return results
    
    def save_results(self, results: Dict, analysis_type: str):
        """
        Save analysis results to JSON file
        
        Args:
            results: Analysis results dictionary
            analysis_type: Type of analysis (for filename)
        """
        output_file = self.results_dir / f"{analysis_type}_results.json"
        
        # Convert numpy types to native Python types for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        results_converted = convert_numpy(results)
        
        with open(output_file, 'w') as f:
            json.dump(results_converted, f, indent=2)
        
        logger.info(f"Results saved to {output_file}")

def main():
    """Main execution function"""
    logger.info("Starting Phase 5: Resistance Mechanism Analysis")
    
    # Initialize resistance analyzer
    analyzer = ResistanceAnalyzer()
    
    try:
        # Load annotated data
        adata = analyzer.load_annotated_data()
        
        # Identify timepoints
        adata = analyzer.identify_timepoints(adata)
        
        # Perform antigen escape analysis
        antigen_results = analyzer.antigen_escape_analysis(adata)
        analyzer.save_results(antigen_results, "antigen_escape")
        
        # Perform CAR-T dysfunction analysis
        dysfunction_results = analyzer.cart_dysfunction_analysis(adata)
        analyzer.save_results(dysfunction_results, "cart_dysfunction")
        
        # Perform microenvironment analysis
        microenv_results = analyzer.microenvironment_analysis(adata)
        analyzer.save_results(microenv_results, "microenvironment")
        
        # Perform trajectory analysis
        trajectory_results = analyzer.trajectory_analysis(adata)
        analyzer.save_results(trajectory_results, "trajectory")
        
        # Save final annotated data with resistance scores
        output_path = analyzer.results_dir / "resistance_analyzed_data.h5ad"
        adata.write(output_path)
        
        logger.info("✓ Resistance mechanism analysis completed successfully!")
        logger.info(f"Results saved to {analyzer.results_dir}")
        
    except Exception as e:
        logger.error(f"✗ Resistance mechanism analysis failed: {e}")
        raise

if __name__ == "__main__":
    main()
