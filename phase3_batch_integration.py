#!/usr/bin/env python3
"""
Phase 3: Batch Integration
BCMA CAR-T Therapy Resistance Analysis Pipeline

This module implements batch effect correction across different studies, patients, 
and experimental batches using Harmony and scVI/scANVI integration methods.

Key features:
- Harmony integration for linear batch correction
- scVI/scANVI for deep learning-based integration
- Integration quality assessment using LISI scores
- Preservation of biological heterogeneity validation

Author: BCMA CAR-T Analysis Pipeline
Date: 2025-06-29
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import scanpy as sc
import anndata as ad
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Union
import warnings
import json

# Integration methods
import harmonypy as hm
import scvi
from scvi.model import SCVI, SCANVI

# Quality assessment
try:
    import lisi
    LISI_AVAILABLE = True
except ImportError:
    LISI_AVAILABLE = False
    warnings.warn("LISI package not available. Integration quality assessment will be limited.")

# Configure scanpy settings
sc.settings.verbosity = 3
sc.settings.set_figure_params(dpi=80, facecolor='white')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_integration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class BatchIntegrator:
    """
    Main class for batch effect correction and integration
    """
    
    def __init__(self, data_dir: str = "results/qc", results_dir: str = "results/integration"):
        """
        Initialize the BatchIntegrator
        
        Args:
            data_dir: Directory containing QC-processed data
            results_dir: Directory for integration results
        """
        self.data_dir = Path(data_dir)
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Integration parameters
        self.integration_params = {
            'harmony': {
                'theta': [2],  # Diversity clustering penalty
                'lamb': [1],   # Ridge regression penalty
                'sigma': 0.1,  # Width of soft kmeans clusters
                'nclust': None,  # Number of clusters (auto-determined)
                'tau': 0,      # Protection against overclustering
                'block_size': 0.05,  # Fraction of cells to update per iteration
                'max_iter_harmony': 10,
                'max_iter_kmeans': 20,
                'epsilon_cluster': 1e-5,
                'epsilon_harmony': 1e-4
            },
            'scvi': {
                'n_layers': 2,
                'n_latent': 30,
                'gene_likelihood': 'nb',
                'dispersion': 'gene',
                'max_epochs': 400,
                'early_stopping': True,
                'early_stopping_patience': 45,
                'early_stopping_min_delta': 0.001,
                'plan_kwargs': {'lr': 1e-3}
            }
        }
        
        logger.info(f"BatchIntegrator initialized")
        logger.info(f"Data directory: {self.data_dir}")
        logger.info(f"Results directory: {self.results_dir}")
    
    def load_datasets(self) -> List[ad.AnnData]:
        """
        Load all QC-processed datasets
        
        Returns:
            List of AnnData objects
        """
        logger.info("Loading QC-processed datasets")
        
        # Find all processed datasets
        dataset_files = list(self.data_dir.glob("*_qc_processed.h5ad"))
        
        if not dataset_files:
            raise FileNotFoundError("No QC-processed datasets found. Please run Phase 2 first.")
        
        datasets = []
        dataset_names = []
        
        for file_path in dataset_files:
            try:
                adata = sc.read_h5ad(file_path)
                dataset_name = file_path.stem.replace('_qc_processed', '')
                
                # Add dataset identifier
                adata.obs['dataset'] = dataset_name
                adata.obs['batch'] = dataset_name  # Use dataset as batch by default
                
                datasets.append(adata)
                dataset_names.append(dataset_name)
                
                logger.info(f"Loaded {dataset_name}: {adata.n_obs} cells, {adata.n_vars} genes")
                
            except Exception as e:
                logger.error(f"Failed to load {file_path}: {e}")
        
        logger.info(f"Successfully loaded {len(datasets)} datasets")
        return datasets
    
    def merge_datasets(self, datasets: List[ad.AnnData]) -> ad.AnnData:
        """
        Merge multiple datasets into a single AnnData object
        
        Args:
            datasets: List of AnnData objects
            
        Returns:
            Merged AnnData object
        """
        logger.info("Merging datasets")
        
        if len(datasets) == 1:
            logger.info("Only one dataset found, no merging needed")
            return datasets[0]
        
        # Find common genes across all datasets
        common_genes = set(datasets[0].var_names)
        for adata in datasets[1:]:
            common_genes = common_genes.intersection(set(adata.var_names))
        
        common_genes = sorted(list(common_genes))
        logger.info(f"Found {len(common_genes)} common genes across datasets")
        
        # Subset datasets to common genes
        datasets_subset = []
        for adata in datasets:
            adata_subset = adata[:, common_genes].copy()
            datasets_subset.append(adata_subset)
        
        # Concatenate datasets
        adata_merged = ad.concat(datasets_subset, join='outer', index_unique='-')
        
        # Ensure batch information is preserved
        if 'batch' not in adata_merged.obs.columns:
            adata_merged.obs['batch'] = adata_merged.obs['dataset']
        
        logger.info(f"Merged dataset: {adata_merged.n_obs} cells, {adata_merged.n_vars} genes")
        logger.info(f"Batches: {adata_merged.obs['batch'].value_counts().to_dict()}")
        
        return adata_merged
    
    def harmony_integration(self, adata: ad.AnnData, batch_key: str = 'batch') -> ad.AnnData:
        """
        Perform Harmony integration
        
        Args:
            adata: AnnData object
            batch_key: Column name for batch information
            
        Returns:
            AnnData object with Harmony-corrected embeddings
        """
        logger.info("Performing Harmony integration")
        
        # Ensure we have PCA
        if 'X_pca' not in adata.obsm:
            logger.info("Computing PCA for Harmony")
            sc.tl.pca(adata, svd_solver='arpack', n_comps=50)
        
        # Run Harmony
        harmony_out = hm.run_harmony(
            adata.obsm['X_pca'],
            adata.obs,
            batch_key,
            **self.integration_params['harmony']
        )
        
        # Store Harmony results
        adata.obsm['X_pca_harmony'] = harmony_out.Z_corr.T
        
        # Compute neighborhood graph on Harmony embeddings
        sc.pp.neighbors(adata, use_rep='X_pca_harmony', key_added='harmony')
        
        # Compute UMAP on Harmony embeddings
        sc.tl.umap(adata, neighbors_key='harmony')
        adata.obsm['X_umap_harmony'] = adata.obsm['X_umap'].copy()
        
        # Perform clustering on Harmony embeddings
        sc.tl.leiden(adata, neighbors_key='harmony', key_added='leiden_harmony', resolution=0.5)
        
        logger.info("Harmony integration completed")
        return adata
    
    def scvi_integration(self, adata: ad.AnnData, batch_key: str = 'batch') -> ad.AnnData:
        """
        Perform scVI integration
        
        Args:
            adata: AnnData object
            batch_key: Column name for batch information
            
        Returns:
            AnnData object with scVI latent representation
        """
        logger.info("Performing scVI integration")
        
        # Setup scVI
        scvi.model.SCVI.setup_anndata(
            adata,
            batch_key=batch_key,
            layer=None  # Use .X (normalized data)
        )
        
        # Create and train scVI model
        model = scvi.model.SCVI(
            adata,
            **{k: v for k, v in self.integration_params['scvi'].items() 
               if k not in ['max_epochs', 'early_stopping', 'early_stopping_patience', 
                           'early_stopping_min_delta', 'plan_kwargs']}
        )
        
        # Train the model
        model.train(
            max_epochs=self.integration_params['scvi']['max_epochs'],
            early_stopping=self.integration_params['scvi']['early_stopping'],
            early_stopping_patience=self.integration_params['scvi']['early_stopping_patience'],
            early_stopping_min_delta=self.integration_params['scvi']['early_stopping_min_delta'],
            plan_kwargs=self.integration_params['scvi']['plan_kwargs']
        )
        
        # Get latent representation
        adata.obsm['X_scvi'] = model.get_latent_representation()
        
        # Compute neighborhood graph on scVI embeddings
        sc.pp.neighbors(adata, use_rep='X_scvi', key_added='scvi')
        
        # Compute UMAP on scVI embeddings
        sc.tl.umap(adata, neighbors_key='scvi')
        adata.obsm['X_umap_scvi'] = adata.obsm['X_umap'].copy()
        
        # Perform clustering on scVI embeddings
        sc.tl.leiden(adata, neighbors_key='scvi', key_added='leiden_scvi', resolution=0.5)
        
        # Store model for later use
        model_path = self.results_dir / "scvi_model"
        model.save(model_path, overwrite=True)
        
        logger.info("scVI integration completed")
        return adata
    
    def assess_integration_quality(self, adata: ad.AnnData, batch_key: str = 'batch') -> Dict:
        """
        Assess integration quality using various metrics
        
        Args:
            adata: Integrated AnnData object
            batch_key: Column name for batch information
            
        Returns:
            Dictionary with integration quality metrics
        """
        logger.info("Assessing integration quality")
        
        metrics = {}
        
        # 1. Silhouette analysis
        from sklearn.metrics import silhouette_score
        
        # Silhouette score for batch mixing (lower is better for integration)
        if 'X_pca_harmony' in adata.obsm:
            sil_batch_harmony = silhouette_score(
                adata.obsm['X_pca_harmony'], 
                adata.obs[batch_key]
            )
            metrics['silhouette_batch_harmony'] = sil_batch_harmony
        
        if 'X_scvi' in adata.obsm:
            sil_batch_scvi = silhouette_score(
                adata.obsm['X_scvi'], 
                adata.obs[batch_key]
            )
            metrics['silhouette_batch_scvi'] = sil_batch_scvi
        
        # 2. LISI scores (if available)
        if LISI_AVAILABLE:
            try:
                if 'X_pca_harmony' in adata.obsm:
                    # Integration LISI (iLISI) - higher is better
                    ilisi_harmony = lisi.compute_lisi(
                        adata.obsm['X_pca_harmony'], 
                        adata.obs, 
                        [batch_key]
                    )
                    metrics['ilisi_harmony'] = np.median(ilisi_harmony[batch_key])
                
                if 'X_scvi' in adata.obsm:
                    ilisi_scvi = lisi.compute_lisi(
                        adata.obsm['X_scvi'], 
                        adata.obs, 
                        [batch_key]
                    )
                    metrics['ilisi_scvi'] = np.median(ilisi_scvi[batch_key])
                    
            except Exception as e:
                logger.warning(f"LISI computation failed: {e}")
        
        # 3. Batch mixing entropy
        def compute_mixing_entropy(embedding, batch_labels, k=50):
            """Compute local batch mixing entropy"""
            from sklearn.neighbors import NearestNeighbors
            
            nbrs = NearestNeighbors(n_neighbors=k).fit(embedding)
            _, indices = nbrs.kneighbors(embedding)
            
            entropies = []
            for i in range(len(embedding)):
                neighbor_batches = batch_labels.iloc[indices[i]]
                batch_counts = neighbor_batches.value_counts(normalize=True)
                entropy = -np.sum(batch_counts * np.log2(batch_counts + 1e-8))
                entropies.append(entropy)
            
            return np.mean(entropies)
        
        if 'X_pca_harmony' in adata.obsm:
            entropy_harmony = compute_mixing_entropy(
                adata.obsm['X_pca_harmony'], 
                adata.obs[batch_key]
            )
            metrics['mixing_entropy_harmony'] = entropy_harmony
        
        if 'X_scvi' in adata.obsm:
            entropy_scvi = compute_mixing_entropy(
                adata.obsm['X_scvi'], 
                adata.obs[batch_key]
            )
            metrics['mixing_entropy_scvi'] = entropy_scvi
        
        logger.info("Integration quality assessment completed")
        return metrics
    
    def generate_integration_plots(self, adata: ad.AnnData, batch_key: str = 'batch'):
        """
        Generate comprehensive integration visualization plots
        
        Args:
            adata: Integrated AnnData object
            batch_key: Column name for batch information
        """
        logger.info("Generating integration plots")
        
        # Create plot directory
        plot_dir = self.results_dir / "plots"
        plot_dir.mkdir(exist_ok=True)
        
        # Set up plotting parameters
        plt.style.use('default')
        
        # 1. Before and after integration comparison
        if 'X_pca' in adata.obsm and 'X_pca_harmony' in adata.obsm:
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle('Integration Results Comparison', fontsize=16)
            
            # Original PCA
            sc.pl.pca(adata, color=batch_key, ax=axes[0,0], show=False, frameon=False)
            axes[0,0].set_title('Original PCA')
            
            # Harmony PCA
            sc.pl.embedding(adata, basis='X_pca_harmony', color=batch_key, 
                           ax=axes[0,1], show=False, frameon=False)
            axes[0,1].set_title('Harmony PCA')
            
            # scVI (if available)
            if 'X_scvi' in adata.obsm:
                sc.pl.embedding(adata, basis='X_scvi', color=batch_key, 
                               ax=axes[0,2], show=False, frameon=False)
                axes[0,2].set_title('scVI Latent Space')
            
            # UMAP comparisons
            if 'X_umap_harmony' in adata.obsm:
                sc.pl.embedding(adata, basis='X_umap_harmony', color=batch_key, 
                               ax=axes[1,0], show=False, frameon=False)
                axes[1,0].set_title('Harmony UMAP')
            
            if 'X_umap_scvi' in adata.obsm:
                sc.pl.embedding(adata, basis='X_umap_scvi', color=batch_key, 
                               ax=axes[1,1], show=False, frameon=False)
                axes[1,1].set_title('scVI UMAP')
            
            # Clustering comparison
            if 'leiden_harmony' in adata.obs:
                sc.pl.embedding(adata, basis='X_umap_harmony', color='leiden_harmony', 
                               ax=axes[1,2], show=False, frameon=False, legend_loc='on data')
                axes[1,2].set_title('Harmony Clustering')
            
            plt.tight_layout()
            plt.savefig(plot_dir / 'integration_comparison.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        # 2. Batch mixing visualization
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # Batch distribution before integration
        if 'X_pca' in adata.obsm:
            sc.pl.pca(adata, color=batch_key, ax=axes[0], show=False, frameon=False)
            axes[0].set_title('Before Integration (PCA)')
        
        # Batch distribution after integration
        if 'X_umap_harmony' in adata.obsm:
            sc.pl.embedding(adata, basis='X_umap_harmony', color=batch_key, 
                           ax=axes[1], show=False, frameon=False)
            axes[1].set_title('After Integration (Harmony)')
        
        plt.tight_layout()
        plt.savefig(plot_dir / 'batch_mixing.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Integration plots saved to {plot_dir}")
    
    def integrate_datasets(self, methods: List[str] = ['harmony', 'scvi']) -> Tuple[ad.AnnData, Dict]:
        """
        Perform complete integration pipeline
        
        Args:
            methods: List of integration methods to apply
            
        Returns:
            Integrated AnnData object and quality metrics
        """
        logger.info("Starting batch integration pipeline")
        
        # Load and merge datasets
        datasets = self.load_datasets()
        adata_merged = self.merge_datasets(datasets)
        
        # Save merged data before integration
        merged_path = self.results_dir / "merged_before_integration.h5ad"
        adata_merged.write(merged_path)
        
        # Apply integration methods
        if 'harmony' in methods:
            adata_merged = self.harmony_integration(adata_merged)
        
        if 'scvi' in methods:
            adata_merged = self.scvi_integration(adata_merged)
        
        # Assess integration quality
        quality_metrics = self.assess_integration_quality(adata_merged)
        
        # Generate visualization plots
        self.generate_integration_plots(adata_merged)
        
        # Save integrated data
        integrated_path = self.results_dir / "integrated_data.h5ad"
        adata_merged.write(integrated_path)
        
        # Save quality metrics
        metrics_path = self.results_dir / "integration_quality_metrics.json"
        with open(metrics_path, 'w') as f:
            json.dump(quality_metrics, f, indent=2)
        
        logger.info(f"Integration completed. Results saved to {self.results_dir}")
        logger.info(f"Quality metrics: {quality_metrics}")
        
        return adata_merged, quality_metrics

def main():
    """Main execution function"""
    logger.info("Starting Phase 3: Batch Integration")
    
    # Initialize batch integrator
    integrator = BatchIntegrator()
    
    # Perform integration
    try:
        adata_integrated, metrics = integrator.integrate_datasets(['harmony', 'scvi'])
        logger.info("✓ Batch integration completed successfully!")
        
        # Print summary
        logger.info(f"Final integrated dataset: {adata_integrated.n_obs} cells, {adata_integrated.n_vars} genes")
        logger.info(f"Integration methods applied: {list(adata_integrated.obsm.keys())}")
        
    except Exception as e:
        logger.error(f"✗ Batch integration failed: {e}")
        raise

if __name__ == "__main__":
    main()
