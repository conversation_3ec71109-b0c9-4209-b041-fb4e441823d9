#!/usr/bin/env python3
"""
Test Script for BCMA CAR-T Therapy Resistance Analysis Pipeline

This script performs basic validation tests to ensure the pipeline is working correctly.
"""

import os
import sys
import logging
import unittest
import tempfile
import shutil
from pathlib import Path
import numpy as np
import pandas as pd
import scanpy as sc
import anndata as ad

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestPipeline(unittest.TestCase):
    """Test cases for the BCMA CAR-T pipeline"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment"""
        cls.test_dir = Path(tempfile.mkdtemp())
        cls.original_dir = Path.cwd()
        os.chdir(cls.test_dir)
        
        # Create test data directory structure
        (cls.test_dir / "data" / "raw").mkdir(parents=True)
        (cls.test_dir / "results").mkdir(parents=True)
        
        logger.info(f"Test directory: {cls.test_dir}")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test environment"""
        os.chdir(cls.original_dir)
        shutil.rmtree(cls.test_dir)
        logger.info("Test cleanup completed")
    
    def create_test_data(self, n_cells=1000, n_genes=2000):
        """Create synthetic test data"""
        # Generate random expression matrix
        np.random.seed(42)
        X = np.random.negative_binomial(5, 0.3, size=(n_cells, n_genes))
        
        # Create gene names
        gene_names = [f"Gene_{i:04d}" for i in range(n_genes)]
        
        # Add some known marker genes
        marker_genes = [
            'TNFRSF17',  # BCMA
            'CD3E', 'CD4', 'CD8A',  # T cell markers
            'CD138', 'PRDM1',  # Plasma cell markers
            'PDCD1', 'CTLA4',  # Exhaustion markers
            'MT-CO1', 'MT-ND1'  # Mitochondrial genes
        ]
        
        for i, gene in enumerate(marker_genes):
            if i < n_genes:
                gene_names[i] = gene
        
        # Create cell barcodes
        cell_barcodes = [f"Cell_{i:04d}" for i in range(n_cells)]
        
        # Create AnnData object
        adata = ad.AnnData(X=X)
        adata.var_names = gene_names
        adata.obs_names = cell_barcodes
        
        # Add metadata
        adata.obs['dataset'] = 'test_dataset'
        adata.obs['batch'] = np.random.choice(['batch1', 'batch2'], n_cells)
        adata.obs['timepoint'] = np.random.choice(['pre_treatment', 'post_treatment', 'relapse'], n_cells)
        adata.obs['sample'] = np.random.choice(['sample1', 'sample2', 'sample3'], n_cells)
        
        return adata
    
    def test_imports(self):
        """Test that all required modules can be imported"""
        logger.info("Testing imports...")
        
        try:
            from phase1_data_collection import DataCollector
            from phase2_quality_control import QualityController
            from phase3_batch_integration import BatchIntegrator
            from phase4_cell_annotation import CellAnnotator
            from phase5_resistance_analysis import ResistanceAnalyzer
            from main_pipeline import BCMAPipeline
            logger.info("✓ All modules imported successfully")
        except ImportError as e:
            self.fail(f"Import failed: {e}")
    
    def test_data_collector(self):
        """Test DataCollector functionality"""
        logger.info("Testing DataCollector...")
        
        from phase1_data_collection import DataCollector
        
        collector = DataCollector(base_dir="data")
        
        # Test directory creation
        self.assertTrue(collector.raw_data_dir.exists())
        self.assertTrue(collector.processed_data_dir.exists())
        self.assertTrue(collector.metadata_dir.exists())
        
        # Test inventory creation
        inventory = collector.create_dataset_inventory()
        self.assertIsInstance(inventory, pd.DataFrame)
        self.assertGreater(len(inventory), 0)
        
        logger.info("✓ DataCollector tests passed")
    
    def test_quality_controller(self):
        """Test QualityController functionality"""
        logger.info("Testing QualityController...")
        
        from phase2_quality_control import QualityController
        
        # Create test data
        adata = self.create_test_data()
        test_file = self.test_dir / "test_data.h5ad"
        adata.write(test_file)
        
        qc = QualityController(
            data_dir=str(self.test_dir),
            results_dir="results/qc"
        )
        
        # Test QC metrics calculation
        adata_qc = qc.calculate_qc_metrics(adata)
        
        # Check that QC metrics were added
        expected_metrics = ['total_counts', 'n_genes_by_counts', 'pct_counts_mt']
        for metric in expected_metrics:
            self.assertIn(metric, adata_qc.obs.columns)
        
        # Test doublet detection
        adata_doublets = qc.detect_doublets(adata_qc)
        self.assertIn('doublet_score', adata_doublets.obs.columns)
        self.assertIn('predicted_doublet', adata_doublets.obs.columns)
        
        logger.info("✓ QualityController tests passed")
    
    def test_batch_integrator(self):
        """Test BatchIntegrator functionality"""
        logger.info("Testing BatchIntegrator...")
        
        from phase3_batch_integration import BatchIntegrator
        
        # Create test data with batches
        adata1 = self.create_test_data(n_cells=500)
        adata1.obs['batch'] = 'batch1'
        adata1.obs['dataset'] = 'dataset1'
        
        adata2 = self.create_test_data(n_cells=500)
        adata2.obs['batch'] = 'batch2'
        adata2.obs['dataset'] = 'dataset2'
        
        # Save test datasets
        qc_dir = self.test_dir / "results" / "qc"
        qc_dir.mkdir(parents=True, exist_ok=True)
        
        adata1.write(qc_dir / "dataset1_qc_processed.h5ad")
        adata2.write(qc_dir / "dataset2_qc_processed.h5ad")
        
        integrator = BatchIntegrator(
            data_dir="results/qc",
            results_dir="results/integration"
        )
        
        # Test dataset loading
        datasets = integrator.load_datasets()
        self.assertEqual(len(datasets), 2)
        
        # Test dataset merging
        merged = integrator.merge_datasets(datasets)
        self.assertEqual(merged.n_obs, 1000)
        
        logger.info("✓ BatchIntegrator tests passed")
    
    def test_cell_annotator(self):
        """Test CellAnnotator functionality"""
        logger.info("Testing CellAnnotator...")
        
        from phase4_cell_annotation import CellAnnotator
        
        # Create test data with some structure
        adata = self.create_test_data()
        
        # Add some PCA for testing
        sc.pp.normalize_total(adata, target_sum=1e4)
        sc.pp.log1p(adata)
        sc.pp.highly_variable_genes(adata)
        sc.tl.pca(adata)
        
        # Save integrated data
        integration_dir = self.test_dir / "results" / "integration"
        integration_dir.mkdir(parents=True, exist_ok=True)
        adata.write(integration_dir / "integrated_data.h5ad")
        
        annotator = CellAnnotator(
            data_dir="results/integration",
            results_dir="results/annotation"
        )
        
        # Test marker score calculation
        adata_scores = annotator.calculate_marker_scores(adata)
        
        # Check that some marker scores were calculated
        score_columns = [col for col in adata_scores.obs.columns if col.endswith('_score')]
        self.assertGreater(len(score_columns), 0)
        
        # Test manual annotation
        adata_annotated = annotator.manual_annotation(adata_scores)
        self.assertIn('manual_annotation', adata_annotated.obs.columns)
        
        logger.info("✓ CellAnnotator tests passed")
    
    def test_resistance_analyzer(self):
        """Test ResistanceAnalyzer functionality"""
        logger.info("Testing ResistanceAnalyzer...")
        
        from phase5_resistance_analysis import ResistanceAnalyzer
        
        # Create test data with annotations
        adata = self.create_test_data()
        
        # Add mock annotations
        adata.obs['consensus_annotation'] = np.random.choice([
            'Malignant Plasma Cells', 'CAR-T CD4+', 'CAR-T CD8+', 
            'CD4+ T Cells', 'CD8+ T Cells', 'B Cells'
        ], adata.n_obs)
        
        # Save annotated data
        annotation_dir = self.test_dir / "results" / "annotation"
        annotation_dir.mkdir(parents=True, exist_ok=True)
        adata.write(annotation_dir / "annotated_data.h5ad")
        
        analyzer = ResistanceAnalyzer(
            data_dir="results/annotation",
            results_dir="results/resistance"
        )
        
        # Test timepoint identification
        adata_timepoints = analyzer.identify_timepoints(adata)
        self.assertIn('timepoint', adata_timepoints.obs.columns)
        
        # Test antigen escape analysis
        antigen_results = analyzer.antigen_escape_analysis(adata_timepoints)
        self.assertIsInstance(antigen_results, dict)
        
        # Test CAR-T dysfunction analysis
        dysfunction_results = analyzer.cart_dysfunction_analysis(adata_timepoints)
        self.assertIsInstance(dysfunction_results, dict)
        
        logger.info("✓ ResistanceAnalyzer tests passed")
    
    def test_main_pipeline(self):
        """Test main pipeline functionality"""
        logger.info("Testing main pipeline...")
        
        from main_pipeline import BCMAPipeline
        
        # Create minimal config
        config = {
            'data_dir': 'data',
            'results_dir': 'results',
            'phases_to_run': [],  # Don't run any phases in test
            'qc_params': {
                'min_genes_per_cell': 100,
                'max_mito_percent': 30
            }
        }
        
        # Test pipeline initialization
        pipeline = BCMAPipeline()
        self.assertIsNotNone(pipeline.config)
        self.assertTrue(pipeline.results_dir.exists())
        
        logger.info("✓ Main pipeline tests passed")

def run_integration_test():
    """Run a simple integration test with synthetic data"""
    logger.info("Running integration test...")
    
    try:
        # Create temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            original_dir = Path.cwd()
            
            try:
                os.chdir(temp_path)
                
                # Create synthetic dataset
                logger.info("Creating synthetic test data...")
                adata = create_synthetic_dataset()
                
                # Save test data
                data_dir = temp_path / "data" / "raw" / "test_dataset"
                data_dir.mkdir(parents=True)
                adata.write(data_dir / "test_data.h5ad")
                
                # Test QC phase
                logger.info("Testing QC phase...")
                from phase2_quality_control import QualityController
                
                qc = QualityController(
                    data_dir=str(data_dir.parent),
                    results_dir="results/qc"
                )
                
                adata_qc, stats = qc.process_dataset(
                    data_dir / "test_data.h5ad", 
                    "test_dataset"
                )
                
                logger.info(f"QC completed: {adata_qc.n_obs} cells retained")
                logger.info("✓ Integration test passed")
                
            finally:
                os.chdir(original_dir)
                
    except Exception as e:
        logger.error(f"Integration test failed: {e}")
        raise

def create_synthetic_dataset(n_cells=2000, n_genes=3000):
    """Create a realistic synthetic dataset for testing"""
    np.random.seed(42)
    
    # Create expression matrix with realistic structure
    X = np.random.negative_binomial(3, 0.2, size=(n_cells, n_genes))
    
    # Add some structure (cell types)
    cell_types = np.random.choice(['plasma', 'cart_cd4', 'cart_cd8', 't_cell', 'b_cell'], n_cells)
    
    # Modify expression based on cell type
    for i, cell_type in enumerate(cell_types):
        if cell_type == 'plasma':
            X[i, :100] *= 3  # Higher expression of first 100 genes
        elif cell_type.startswith('cart'):
            X[i, 100:200] *= 2  # CAR-T specific genes
        elif cell_type == 't_cell':
            X[i, 200:300] *= 2  # T cell genes
    
    # Create gene names with known markers
    gene_names = []
    marker_genes = [
        'TNFRSF17', 'CD3E', 'CD4', 'CD8A', 'CD138', 'PRDM1',
        'PDCD1', 'CTLA4', 'TIGIT', 'LAG3', 'HAVCR2',
        'MT-CO1', 'MT-ND1', 'MT-ATP6', 'MT-CYB'
    ]
    
    for i in range(n_genes):
        if i < len(marker_genes):
            gene_names.append(marker_genes[i])
        else:
            gene_names.append(f"Gene_{i:04d}")
    
    # Create AnnData object
    adata = ad.AnnData(X=X)
    adata.var_names = gene_names
    adata.obs_names = [f"Cell_{i:04d}" for i in range(n_cells)]
    
    # Add metadata
    adata.obs['cell_type'] = cell_types
    adata.obs['batch'] = np.random.choice(['batch1', 'batch2', 'batch3'], n_cells)
    adata.obs['timepoint'] = np.random.choice(['pre_treatment', 'post_treatment', 'relapse'], n_cells)
    adata.obs['patient'] = np.random.choice(['patient1', 'patient2', 'patient3'], n_cells)
    
    return adata

def main():
    """Main test function"""
    logger.info("=" * 60)
    logger.info("BCMA CAR-T Pipeline Test Suite")
    logger.info("=" * 60)
    
    # Run unit tests
    logger.info("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration test
    logger.info("\nRunning integration test...")
    try:
        run_integration_test()
        logger.info("✓ All tests passed!")
    except Exception as e:
        logger.error(f"✗ Integration test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
