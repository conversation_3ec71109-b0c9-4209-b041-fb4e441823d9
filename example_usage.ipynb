{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BCMA CAR-T Therapy Resistance Analysis Pipeline\n", "## Example Usage Notebook\n", "\n", "This notebook demonstrates how to use the BCMA CAR-T therapy resistance analysis pipeline for studying resistance mechanisms in multiple myeloma patients.\n", "\n", "### Pipeline Overview\n", "\n", "The pipeline consists of 5 main phases:\n", "1. **Data Collection** - Download and organize public datasets\n", "2. **Quality Control** - Remove low-quality cells and doublets\n", "3. **Batch Integration** - Correct for technical batch effects\n", "4. **Cell Type Annotation** - Identify and annotate cell populations\n", "5. **Resistance Analysis** - Analyze resistance mechanisms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Installation\n", "\n", "First, ensure all dependencies are installed:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run setup script (only needed once)\n", "# !python setup.py\n", "\n", "# Import required libraries\n", "import sys\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "import scanpy as sc\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import json\n", "\n", "# Configure scanpy\n", "sc.settings.verbosity = 3\n", "sc.settings.set_figure_params(dpi=80, facecolor='white')\n", "\n", "print(\"Setup completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Method 1: Run Complete Pipeline\n", "\n", "The simplest way to run the entire pipeline:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the main pipeline\n", "from main_pipeline import BCMAPipeline\n", "\n", "# Initialize pipeline with default configuration\n", "pipeline = BCMAPipeline()\n", "\n", "# Run all phases\n", "success = pipeline.run_pipeline()\n", "\n", "if success:\n", "    print(\"🎉 Pipeline completed successfully!\")\n", "else:\n", "    print(\"❌ Pipeline failed. Check logs for details.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Method 2: Run Individual Phases\n", "\n", "For more control, you can run individual phases:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Phase 1: Data Collection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from phase1_data_collection import DataCollector\n", "\n", "# Initialize data collector\n", "collector = DataCollector(base_dir=\"data\")\n", "\n", "# Check dependencies\n", "if collector.check_dependencies():\n", "    print(\"✓ All dependencies available\")\n", "    \n", "    # Download datasets (this may take a while)\n", "    # download_status = collector.download_all_datasets()\n", "    # print(f\"Download status: {download_status}\")\n", "    \n", "    # Create inventory\n", "    inventory = collector.create_dataset_inventory()\n", "    print(\"Dataset inventory:\")\n", "    print(inventory)\n", "else:\n", "    print(\"❌ Missing dependencies. Please install wget/curl.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Phase 2: Quality Control"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from phase2_quality_control import QualityController\n", "\n", "# Initialize quality controller\n", "qc = QualityController(\n", "    data_dir=\"data/raw\",\n", "    results_dir=\"results/qc\"\n", ")\n", "\n", "# Display QC parameters\n", "print(\"QC Parameters:\")\n", "for param, value in qc.qc_params.items():\n", "    print(f\"  {param}: {value}\")\n", "\n", "# Example: Process a single dataset (if available)\n", "# adata, stats = qc.process_dataset(\"path/to/dataset.h5ad\", \"sample_name\")\n", "# print(f\"QC Statistics: {stats}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Phase 3: Batch Integration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from phase3_batch_integration import BatchIntegrator\n", "\n", "# Initialize batch integrator\n", "integrator = BatchIntegrator(\n", "    data_dir=\"results/qc\",\n", "    results_dir=\"results/integration\"\n", ")\n", "\n", "# Display integration parameters\n", "print(\"Integration Parameters:\")\n", "print(f\"Harmony: {integrator.integration_params['harmony']}\")\n", "print(f\"scVI: {integrator.integration_params['scvi']}\")\n", "\n", "# Example: Run integration (if QC data available)\n", "# adata_integrated, metrics = integrator.integrate_datasets(['harmony'])\n", "# print(f\"Integration metrics: {metrics}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Phase 4: Cell Type Annotation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from phase4_cell_annotation import CellAnnotator\n", "\n", "# Initialize cell annotator\n", "annotator = CellAnnotator(\n", "    data_dir=\"results/integration\",\n", "    results_dir=\"results/annotation\"\n", ")\n", "\n", "# Display marker genes\n", "print(\"Marker genes for key cell types:\")\n", "for cell_type, genes in list(annotator.marker_genes.items())[:5]:\n", "    print(f\"  {cell_type}: {genes[:3]}...\")  # Show first 3 genes\n", "\n", "# Display BCMA targets\n", "print(\"\\nBCMA and alternative targets:\")\n", "for target_type, genes in annotator.bcma_targets.items():\n", "    print(f\"  {target_type}: {genes}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Phase 5: Resistance Mechanism Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from phase5_resistance_analysis import ResistanceAnalyzer\n", "\n", "# Initialize resistance analyzer\n", "analyzer = ResistanceAnalyzer(\n", "    data_dir=\"results/annotation\",\n", "    results_dir=\"results/resistance\"\n", ")\n", "\n", "# Display resistance gene sets\n", "print(\"Resistance mechanism gene sets:\")\n", "for mechanism, pathways in analyzer.resistance_genes.items():\n", "    print(f\"\\n{mechanism.upper()}:\")\n", "    for pathway, genes in pathways.items():\n", "        print(f\"  {pathway}: {genes[:3]}...\")  # Show first 3 genes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Method 3: Custom Configuration\n", "\n", "You can customize the pipeline behavior using a configuration file:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and display default configuration\n", "with open('config.json', 'r') as f:\n", "    config = json.load(f)\n", "\n", "print(\"Current configuration:\")\n", "print(json.dumps(config, indent=2)[:1000] + \"...\")  # Show first 1000 characters\n", "\n", "# Example: Modify QC parameters\n", "custom_config = config.copy()\n", "custom_config['quality_control']['max_mito_percent'] = 15  # More stringent\n", "custom_config['quality_control']['min_genes_per_cell'] = 300  # More stringent\n", "\n", "# Save custom configuration\n", "with open('custom_config.json', 'w') as f:\n", "    json.dump(custom_config, f, indent=2)\n", "\n", "print(\"\\nCustom configuration saved to 'custom_config.json'\")\n", "print(\"Run with: python main_pipeline.py --config custom_config.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results Analysis\n", "\n", "After running the pipeline, you can analyze the results:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if results are available\n", "results_dir = Path(\"results\")\n", "\n", "if results_dir.exists():\n", "    print(\"Available results:\")\n", "    for subdir in results_dir.iterdir():\n", "        if subdir.is_dir():\n", "            files = list(subdir.glob(\"*.h5ad\")) + list(subdir.glob(\"*.json\"))\n", "            print(f\"  {subdir.name}: {len(files)} files\")\n", "            \n", "    # Load pipeline summary if available\n", "    summary_file = results_dir / \"pipeline_summary.json\"\n", "    if summary_file.exists():\n", "        with open(summary_file, 'r') as f:\n", "            summary = json.load(f)\n", "        \n", "        print(\"\\nPipeline Summary:\")\n", "        print(f\"  Success rate: {summary.get('success_rate', 0):.1%}\")\n", "        print(f\"  Duration: {summary.get('duration_seconds', 0):.1f} seconds\")\n", "        print(f\"  Successful phases: {summary.get('successful_phases', 0)}/{summary.get('total_phases', 0)}\")\n", "else:\n", "    print(\"No results found. Run the pipeline first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load and Explore Final Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load final annotated data (if available)\n", "final_data_path = Path(\"results/resistance/resistance_analyzed_data.h5ad\")\n", "\n", "if final_data_path.exists():\n", "    adata = sc.read_h5ad(final_data_path)\n", "    \n", "    print(f\"Final dataset: {adata.n_obs} cells, {adata.n_vars} genes\")\n", "    print(f\"Cell types: {adata.obs['consensus_annotation'].value_counts().to_dict()}\")\n", "    \n", "    # Plot UMAP with cell type annotations\n", "    sc.pl.umap(adata, color='consensus_annotation', legend_loc='on data')\n", "    plt.title('Cell Type Annotations')\n", "    plt.show()\n", "    \n", "    # Plot BCMA expression if available\n", "    if 'TNFRSF17' in adata.var_names:\n", "        sc.pl.umap(adata, color='TNFRSF17', cmap='viridis')\n", "        plt.title('BCMA (TNFRSF17) Expression')\n", "        plt.show()\n", "    \n", "else:\n", "    print(\"Final results not found. Run the complete pipeline first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze Resistance Mechanisms"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load resistance analysis results\n", "resistance_dir = Path(\"results/resistance\")\n", "\n", "if resistance_dir.exists():\n", "    # Load antigen escape results\n", "    antigen_file = resistance_dir / \"antigen_escape_results.json\"\n", "    if antigen_file.exists():\n", "        with open(antigen_file, 'r') as f:\n", "            antigen_results = json.load(f)\n", "        print(\"Antigen Escape Analysis:\")\n", "        print(json.dumps(antigen_results, indent=2)[:500] + \"...\")\n", "    \n", "    # Load CAR-T dysfunction results\n", "    dysfunction_file = resistance_dir / \"cart_dysfunction_results.json\"\n", "    if dysfunction_file.exists():\n", "        with open(dysfunction_file, 'r') as f:\n", "            dysfunction_results = json.load(f)\n", "        print(\"\\nCAR-T Dysfunction Analysis:\")\n", "        print(json.dumps(dysfunction_results, indent=2)[:500] + \"...\")\n", "    \n", "    # Load microenvironment results\n", "    microenv_file = resistance_dir / \"microenvironment_results.json\"\n", "    if microenv_file.exists():\n", "        with open(microenv_file, 'r') as f:\n", "            microenv_results = json.load(f)\n", "        print(\"\\nMicroenvironment Analysis:\")\n", "        print(json.dumps(microenv_results, indent=2)[:500] + \"...\")\n", "        \n", "else:\n", "    print(\"Resistance analysis results not found.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Troubleshooting\n", "\n", "Common issues and solutions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check log files for errors\n", "log_files = [\n", "    \"pipeline.log\",\n", "    \"data_collection.log\", \n", "    \"quality_control.log\",\n", "    \"batch_integration.log\",\n", "    \"cell_annotation.log\",\n", "    \"resistance_analysis.log\"\n", "]\n", "\n", "print(\"Available log files:\")\n", "for log_file in log_files:\n", "    if Path(log_file).exists():\n", "        size = Path(log_file).stat().st_size\n", "        print(f\"  ✓ {log_file} ({size} bytes)\")\n", "    else:\n", "        print(f\"  ✗ {log_file} (not found)\")\n", "\n", "# Check system resources\n", "import psutil\n", "\n", "print(f\"\\nSystem Resources:\")\n", "print(f\"  CPU cores: {psutil.cpu_count()}\")\n", "print(f\"  Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB\")\n", "print(f\"  Available memory: {psutil.virtual_memory().available / (1024**3):.1f} GB\")\n", "print(f\"  Disk space: {psutil.disk_usage('.').free / (1024**3):.1f} GB free\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Steps\n", "\n", "After running the pipeline successfully:\n", "\n", "1. **Review Results**: Examine the generated plots and analysis results\n", "2. **Validate Findings**: Cross-reference with literature and experimental data\n", "3. **Export Data**: Use the processed data for further analysis or publication\n", "4. **Customize Analysis**: Modify parameters and re-run specific phases as needed\n", "5. **Generate Reports**: Create publication-ready figures and summaries\n", "\n", "For detailed documentation, see the README.md file."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}